{"openapi": "3.1.0", "info": {"title": "游戏战术室 API", "description": "游戏战术室后端API文档", "version": "1.0.0"}, "servers": [{"url": "http://127.0.0.1:8080", "description": "Generated server url"}], "security": [{"Bearer Authentication": []}], "tags": [{"name": "认证管理", "description": "用户认证相关接口"}, {"name": "战事分组管理", "description": "战事分组相关接口"}, {"name": "联盟通知管理", "description": "联盟活动通知相关接口"}, {"name": "官职抢夺", "description": "官职抢夺相关接口"}, {"name": "南蛮入侵分组管理", "description": "南蛮入侵分组相关接口"}, {"name": "吕布星级管理", "description": "吕布星级相关接口"}, {"name": "战事管理", "description": "战事相关接口"}, {"name": "消息订阅管理", "description": "微信消息订阅相关接口"}, {"name": "游戏账号管理", "description": "游戏账号相关接口"}, {"name": "联盟管理", "description": "联盟相关接口"}, {"name": "王朝管理", "description": "王朝相关接口"}, {"name": "查询接口", "description": "各种查询相关接口"}, {"name": "演武场通知管理", "description": "演武场通知相关接口"}, {"name": "联盟成员管理", "description": "联盟成员相关接口"}], "paths": {"/api/war-groups/{groupId}": {"put": {"tags": ["战事分组管理"], "summary": "更新战事分组", "operationId": "updateWarGroup", "parameters": [{"name": "groupId", "in": "path", "description": "分组ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWarGroupRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarGroup"}}}}}}, "delete": {"tags": ["战事分组管理"], "summary": "删除战事分组", "operationId": "deleteWarGroup", "parameters": [{"name": "groupId", "in": "path", "description": "分组ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/war-groups/use-tactics/{allianceId}": {"put": {"tags": ["战事分组管理"], "summary": "使用战术", "operationId": "useTactics", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAccountWarArrangementResponse"}}}}}}}, "/api/game-accounts/{accountId}": {"get": {"tags": ["游戏账号管理"], "summary": "获取游戏账号详情", "operationId": "getGameAccountById", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}, "put": {"tags": ["游戏账号管理"], "summary": "更新游戏账号", "operationId": "updateGameAccount", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGameAccountRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}, "delete": {"tags": ["游戏账号管理"], "summary": "删除游戏账号", "operationId": "deleteGameAccount", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/dynasties/{dynastyId}": {"get": {"tags": ["王朝管理"], "summary": "获取王朝详情", "operationId": "getDynastyDetail", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynastyDetailResponse"}}}}}}, "put": {"tags": ["王朝管理"], "summary": "更新王朝", "operationId": "updateDynasty", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDynastyRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynasty"}}}}}}, "delete": {"tags": ["王朝管理"], "summary": "删除王朝", "operationId": "deleteDynasty", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/arena-notifications/settings": {"get": {"tags": ["演武场通知管理"], "summary": "获取演武场通知设置", "operationId": "getArenaNotificationSetting", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}, "put": {"tags": ["演武场通知管理"], "summary": "更新演武场通知设置", "operationId": "updateArenaNotificationSetting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateArenaNotificationRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseUser"}}}}}}}, "/api/alliances/{allianceId}": {"get": {"tags": ["联盟管理"], "summary": "获取联盟详情", "operationId": "getAllianceById", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}, "put": {"tags": ["联盟管理"], "summary": "更新联盟", "operationId": "updateAlliance", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAllianceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}, "delete": {"tags": ["联盟管理"], "summary": "删除联盟", "operationId": "deleteAlliance", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/alliances/war-limits": {"put": {"tags": ["联盟管理"], "summary": "更新官渡战事人数上限", "operationId": "updateWarLimits", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateWarLimitsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}}, "/api/alliances/approval-settings": {"put": {"tags": ["联盟管理"], "summary": "更新联盟审核设置", "operationId": "updateAllianceApprovalSettings", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAllianceApprovalSettingsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}}, "/api/alliance-notifications/settings": {"put": {"tags": ["联盟通知管理"], "summary": "更新账号通知设置", "operationId": "updateNotificationSettings", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNotificationSettingsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/wars/use-tactic/{allianceId}": {"post": {"tags": ["战事管理"], "summary": "联盟使用预制战术", "operationId": "useTactic", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UseTacticRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/wars/remove-from-war/{accountId}": {"post": {"tags": ["战事管理"], "summary": "将成员移除出指定战事", "operationId": "removeFromWar", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseObject"}}}}}}}, "/api/wars/move-guan-du/{accountId}": {"post": {"tags": ["战事管理"], "summary": "调动成员参加的战事（官渡一与官渡二之间互相调动）", "operationId": "moveWarApplication", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarArrangement"}}}}}}}, "/api/wars/cancel-apply": {"post": {"tags": ["战事管理"], "summary": "取消申请参加战事", "operationId": "cancelApplyForWar", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/wars/apply": {"post": {"tags": ["战事管理"], "summary": "申请参加战事", "operationId": "applyForWar", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarApplication"}}}}}}}, "/api/wars/applications/{applicationId}/process": {"post": {"tags": ["战事管理"], "summary": "处理战事申请", "operationId": "processWarApplication", "parameters": [{"name": "applicationId", "in": "path", "description": "申请ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessApplicationRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarApplication"}}}}}}}, "/api/wars/add-to-war/{accountId}": {"post": {"tags": ["战事管理"], "summary": "盟主直接添加成员到战事中", "operationId": "addToWar", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarArrangement"}}}}}}}, "/api/war-groups": {"post": {"tags": ["战事分组管理"], "summary": "创建战事分组", "operationId": "createWarGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWarGroupRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarGroup"}}}}}}}, "/api/war-groups/clear-arrangements": {"post": {"tags": ["战事分组管理"], "summary": "清空战事安排（支持可选通知）", "operationId": "clearWarArrangementsWithNotification", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClearWarArrangementsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/war-groups/arrange": {"post": {"tags": ["战事分组管理"], "summary": "安排成员到战事分组", "operationId": "arrangeMember", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArrangeMemberRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarArrangement"}}}}}}}, "/api/message-subscription/clear": {"post": {"tags": ["消息订阅管理"], "summary": "清空订阅数量", "operationId": "clearSubscription", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/message-subscription/add": {"post": {"tags": ["消息订阅管理"], "summary": "增加消息订阅数量", "operationId": "addSubscriptionCount", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseUser"}}}}}}}, "/api/game-accounts": {"post": {"tags": ["游戏账号管理"], "summary": "创建游戏账号", "operationId": "createGameAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGameAccountRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/game-accounts/{accountId}/join-dynasty": {"post": {"tags": ["游戏账号管理"], "summary": "账号加入王朝", "operationId": "joinDynasty", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinDynastyRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/dynasties": {"post": {"tags": ["王朝管理"], "summary": "创建王朝", "operationId": "createDynasty", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDynastyRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynasty"}}}}}}}, "/api/dynasties/{dynastyId}/toggle-grab": {"post": {"tags": ["王朝管理"], "summary": "开启或关闭官职抢夺", "operationId": "toggle<PERSON><PERSON><PERSON>nabled", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "enabled", "in": "query", "description": "是否开启", "required": true, "schema": {"type": "boolean"}, "example": true}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynasty"}}}}}}}, "/api/dynasties/{dynastyId}/positions/grab": {"post": {"tags": ["官职抢夺"], "summary": "抢夺官职", "operationId": "grabPosition", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "accountId", "in": "query", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrabPositionRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponsePositionGrab"}}}}}}}, "/api/dynasties/{dynastyId}/positions/grab-time": {"get": {"tags": ["官职抢夺"], "summary": "查询官职抢夺时间的设置信息", "operationId": "getPositionGrabTime", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "positionType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["TAI_WEI", "SHANG_SHU_LING"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynastyPosition"}}}}}}, "post": {"tags": ["官职抢夺"], "summary": "设置官职抢夺时间", "operationId": "setPositionGrabTime", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetPositionGrabTimeRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynastyPosition"}}}}}}}, "/api/dynasties/accounts/{accountId}/leave": {"post": {"tags": ["王朝管理"], "summary": "账号退出王朝", "operationId": "leaveDynasty", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/barbarian-groups": {"post": {"tags": ["南蛮入侵分组管理"], "summary": "创建南蛮分组", "operationId": "createBarbarianGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBarbarianGroupRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBarbarianGroup"}}}}}}}, "/api/barbarian-groups/join": {"post": {"tags": ["南蛮入侵分组管理"], "summary": "加入南蛮分组", "operationId": "joinBarbarianGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinBarbarianGroupRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/barbarian-groups/create-and-join": {"post": {"tags": ["南蛮入侵分组管理"], "summary": "创建南蛮分组并加入", "operationId": "createAndJoinBarbarianGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAndJoinBarbarianGroupRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/auth/login": {"post": {"tags": ["认证管理"], "summary": "微信小程序登录", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseLoginResponse"}}}}}}}, "/api/alliances": {"post": {"tags": ["联盟管理"], "summary": "创建联盟", "operationId": "createAlliance", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAllianceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}}, "/api/alliances/{allianceId}/transfer": {"post": {"tags": ["联盟管理"], "summary": "转交联盟", "operationId": "transferAlliance", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferAllianceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}}, "/api/alliance-notifications/send": {"post": {"tags": ["联盟通知管理"], "summary": "发送联盟活动通知", "operationId": "sendAllianceNotification", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendAllianceNotificationRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/alliance-members/apply": {"post": {"tags": ["联盟成员管理"], "summary": "申请加入联盟", "operationId": "applyToJoinAlliance", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinAllianceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAllianceApplication"}}}}}}}, "/api/alliance-members/applications/{applicationId}/process": {"post": {"tags": ["联盟成员管理"], "summary": "处理加入联盟申请", "operationId": "processApplication", "parameters": [{"name": "applicationId", "in": "path", "description": "申请ID", "required": true, "schema": {"type": "string"}, "example": 1}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessApplicationRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAllianceApplication"}}}}}}}, "/api/wars/alliances/{allianceId}/applications": {"get": {"tags": ["战事管理"], "summary": "获取联盟战事申请列表", "operationId": "getPendingWarApplications", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListWarApplication"}}}}}}}, "/api/wars/alliance/{allianceId}/limit-status": {"get": {"tags": ["战事管理"], "summary": "获取联盟官渡战事人数上限状态", "operationId": "getWarLimitStatus", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarLimitStatusResponse"}}}}}}}, "/api/wars/accounts/{accountId}/applications": {"get": {"tags": ["战事管理"], "summary": "获取账号战事申请历史", "operationId": "getAccountWarApplications", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListWarApplication"}}}}}}}, "/api/war-groups/alliances/{allianceId}": {"get": {"tags": ["战事分组管理"], "summary": "获取联盟战事分组列表", "operationId": "getWarGroups", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListWarGroup"}}}}}}}, "/api/war-groups/alliances/{allianceId}/arrangements": {"get": {"tags": ["战事分组管理"], "summary": "获取战事人员安排（原始数据）", "operationId": "getWarArrangements", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型（可选）", "required": false, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListWarArrangement"}}}}}}, "delete": {"tags": ["战事分组管理"], "summary": "清空战事安排", "operationId": "clearWarArrangements", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/war-groups/alliances/{allianceId}/arrangements/detail": {"get": {"tags": ["战事分组管理"], "summary": "获取战事人员安排详情（包含分组和机动人员）", "operationId": "getWarArrangementDetail", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "warType", "in": "query", "description": "战事类型", "required": true, "schema": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseWarArrangementResponse"}}}}}}}, "/api/war-groups/accounts/{accountId}/arrangements": {"get": {"tags": ["战事分组管理"], "summary": "获取账号的战事安排详情", "operationId": "getAccountWarArrangements", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAccountWarArrangementResponse"}}}}}}}, "/api/query/alliances/{allianceId}/war-applications": {"get": {"tags": ["查询接口"], "summary": "查询申请加入官渡战事的申请列表", "operationId": "getAllianceWarApplications", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseMapStringListWarApplication"}}}}}}}, "/api/query/alliances/{allianceId}/detail": {"get": {"tags": ["查询接口"], "summary": "获取联盟详细信息", "operationId": "getAllianceDetail", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAllianceDetailResponse"}}}}}}}, "/api/query/accounts/{accountId}/war-applications": {"get": {"tags": ["查询接口"], "summary": "查询账号申请加入战事的状态", "operationId": "getAccountWarApplicationStatus", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListWarApplication"}}}}}}}, "/api/query/accounts/{accountId}/alliance-applications": {"get": {"tags": ["查询接口"], "summary": "查询账号申请加入联盟的状态", "operationId": "getAccountAllianceApplicationStatus", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListAllianceApplication"}}}}}}}, "/api/message-subscription/count": {"get": {"tags": ["消息订阅管理"], "summary": "获取当前可接收消息数量", "operationId": "getSubscriptionCount", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseInteger"}}}}}}}, "/api/message-subscription/check/{requiredCount}": {"get": {"tags": ["消息订阅管理"], "summary": "检查是否有足够的消息订阅数量", "operationId": "checkSubscription", "parameters": [{"name": "requiredCount", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBoolean"}}}}}}}, "/api/lvbu-star-levels/valid-levels": {"get": {"tags": ["吕布星级管理"], "summary": "获取所有有效的吕布星级值", "operationId": "getValidStarLevels", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListBigDecimal"}}}}}}}, "/api/game-accounts/my": {"get": {"tags": ["游戏账号管理"], "summary": "获取我的游戏账号列表", "operationId": "getMyGameAccounts", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListGameAccount"}}}}}}}, "/api/dynasties/{dynastyId}/positions/grab-results": {"get": {"tags": ["官职抢夺"], "summary": "获取官职抢夺结果", "operationId": "getGrabResults", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "dutyDate", "in": "query", "description": "任职日期", "required": true, "schema": {"type": "string", "format": "date"}, "example": "2025/08/12"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponsePositionGrabResultResponse"}}}}}}}, "/api/dynasties/my": {"get": {"tags": ["王朝管理"], "summary": "获取我创建的王朝列表", "operationId": "getMyDynasties", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListDynasty"}}}}}}}, "/api/dynasties/code/{code}": {"get": {"tags": ["王朝管理"], "summary": "通过编码获取王朝信息", "operationId": "getDynastyByCode", "parameters": [{"name": "code", "in": "path", "description": "王朝编码", "required": true, "schema": {"type": "string"}, "example": "ABC123"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseDynasty"}}}}}}}, "/api/barbarian-groups/{groupId}/detail": {"get": {"tags": ["南蛮入侵分组管理"], "summary": "获取分组详情（包含成员信息）", "operationId": "getBarbarianGroupDetail", "parameters": [{"name": "groupId", "in": "path", "description": "分组ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBarbarianGroupDetailResponse"}}}}}}}, "/api/barbarian-groups/alliance/{allianceId}": {"get": {"tags": ["南蛮入侵分组管理"], "summary": "获取联盟的所有南蛮分组", "operationId": "getAllianceBarbarianGroups", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListBarbarianGroupResponse"}}}}}}}, "/api/barbarian-groups/alliance/{allianceId}/queue/{queueCount}": {"get": {"tags": ["南蛮入侵分组管理"], "summary": "根据队列数量查询联盟中的南蛮分组", "operationId": "getBarbarianGroupsByQueueCount", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "queueCount", "in": "path", "description": "队列数量", "required": true, "schema": {"type": "integer", "format": "int32"}, "example": 3}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListBarbarianGroupResponse"}}}}}}}, "/api/barbarian-groups/account/{accountId}": {"get": {"tags": ["南蛮入侵分组管理"], "summary": "获取账号所在的南蛮分组", "operationId": "getAccountBarbarianGroup", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseBarbarianGroupResponse"}}}}}}}, "/api/auth/user-info": {"get": {"tags": ["认证管理"], "summary": "获取用户信息", "operationId": "getUserInfo", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseUser"}}}}}}}, "/api/auth/user-complete-info": {"get": {"tags": ["认证管理"], "summary": "获取用户完整信息（包含联盟和账号）", "operationId": "getUserCompleteInfo", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseUserInfoResponse"}}}}}}}, "/api/alliances/my": {"get": {"tags": ["联盟管理"], "summary": "获取我创建的联盟列表", "operationId": "getMyAlliances", "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListAlliance"}}}}}}}, "/api/alliances/code/{code}": {"get": {"tags": ["联盟管理"], "summary": "通过编码获取联盟信息", "operationId": "getAllianceByCode", "parameters": [{"name": "code", "in": "path", "description": "联盟编码", "required": true, "schema": {"type": "string"}, "example": "ABC123"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseAlliance"}}}}}}}, "/api/alliance-members/alliances/{allianceId}/members": {"get": {"tags": ["联盟成员管理"], "summary": "获取联盟成员列表", "operationId": "getAllianceMembers", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListGameAccount"}}}}}}}, "/api/alliance-members/alliances/{allianceId}/applications": {"get": {"tags": ["联盟成员管理"], "summary": "获取联盟待处理申请列表", "operationId": "getPendingApplications", "parameters": [{"name": "allianceId", "in": "path", "description": "联盟ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListAllianceApplication"}}}}}}}, "/api/alliance-members/accounts/{accountId}/applications": {"get": {"tags": ["联盟成员管理"], "summary": "获取账号申请历史", "operationId": "getAccountApplications", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseListAllianceApplication"}}}}}}}, "/api/dynasties/{dynastyId}/grab-results": {"delete": {"tags": ["王朝管理"], "summary": "清空所有抢夺结果", "operationId": "clearAllGrabResults", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/dynasties/{dynastyId}/grab-results/{positionType}": {"delete": {"tags": ["王朝管理"], "summary": "清空指定官职的抢夺结果", "operationId": "clearPositionGrabResults", "parameters": [{"name": "dynastyId", "in": "path", "description": "王朝ID", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "positionType", "in": "path", "description": "官职类型", "required": true, "schema": {"type": "string", "enum": ["TAI_WEI", "SHANG_SHU_LING"]}, "example": "TAI_WEI"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}, "/api/barbarian-groups/leave/{accountId}": {"delete": {"tags": ["南蛮入侵分组管理"], "summary": "离开南蛮分组", "operationId": "leaveBarbarianGroup", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseGameAccount"}}}}}}}, "/api/alliance-members/accounts/{accountId}": {"delete": {"tags": ["联盟成员管理"], "summary": "移除联盟成员", "operationId": "removeMember", "parameters": [{"name": "accountId", "in": "path", "description": "账号ID", "required": true, "schema": {"type": "string"}, "example": 1}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApiResponseVoid"}}}}}}}}, "components": {"schemas": {"ApiResponseVoid": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"description": "响应数据"}}}, "UpdateWarGroupRequest": {"type": "object", "description": "更新战事分组请求", "properties": {"groupName": {"type": "string", "description": "分组名称"}, "groupTask": {"type": "string", "description": "分组任务"}}}, "Alliance": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "code": {"type": "string"}, "serverId": {"type": "integer", "format": "int32"}, "leaderId": {"type": "string"}, "allianceJoinApprovalRequired": {"type": "boolean"}, "warJoinApprovalRequired": {"type": "boolean"}, "guanduOneLimit": {"type": "integer", "format": "int32"}, "guanduTwoLimit": {"type": "integer", "format": "int32"}, "leader": {"$ref": "#/components/schemas/User"}}}, "ApiResponseWarGroup": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/WarGroup", "description": "响应数据"}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "openid": {"type": "string"}, "nickname": {"type": "string"}, "avatarUrl": {"type": "string"}, "phone": {"type": "string"}, "messageSubscriptionCount": {"type": "integer", "format": "int32"}, "arenaNotificationEnabled": {"type": "boolean"}}}, "WarGroup": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "allianceId": {"type": "string"}, "warType": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "groupName": {"type": "string"}, "groupTask": {"type": "string"}, "alliance": {"$ref": "#/components/schemas/Alliance"}}}, "AccountWarArrangementResponse": {"type": "object", "description": "账号战事安排响应", "properties": {"accountId": {"type": "string", "description": "账号ID"}, "account": {"$ref": "#/components/schemas/GameAccount", "description": "账号信息"}, "warArrangements": {"type": "array", "description": "战事安排列表", "items": {"$ref": "#/components/schemas/WarArrangementDetail"}}}}, "ApiResponseAccountWarArrangementResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/AccountWarArrangementResponse", "description": "响应数据"}}}, "Dynasty": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "code": {"type": "string"}, "serverId": {"type": "integer", "format": "int32"}, "emperorId": {"type": "string"}, "grabEnabled": {"type": "boolean"}, "emperor": {"$ref": "#/components/schemas/User"}}}, "GameAccount": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "userId": {"type": "string"}, "serverId": {"type": "integer", "format": "int32"}, "accountName": {"type": "string"}, "powerValue": {"type": "string"}, "damageBonus": {"type": "number"}, "troopLevel": {"type": "integer", "format": "int32"}, "rallyCapacity": {"type": "integer", "format": "int32"}, "troopQuantity": {"type": "string"}, "lvbuStarLevel": {"type": "number"}, "allianceId": {"type": "string"}, "dynastyId": {"type": "string"}, "barbarianGroupId": {"type": "string"}, "memberTier": {"type": "string", "enum": ["TIER_1", "TIER_2", "TIER_3", "TIER_4", "TIER_5"]}, "notificationTypes": {"type": "array", "items": {"type": "string", "enum": ["SAN_YING_ZHAN_LV_BU", "GUAN_DU_BAO_MING", "ZHU_JIU_LUN_YING_XIONG", "NAN_MAN_RU_QIN", "GONG_CHENG", "SHOU_CHENG", "SHUA_GONG_XUN", "YAN_WU_CHANG"]}, "uniqueItems": true}, "user": {"$ref": "#/components/schemas/User"}, "alliance": {"$ref": "#/components/schemas/Alliance"}, "dynasty": {"$ref": "#/components/schemas/Dynasty"}}, "required": ["lvbuStarLevel"]}, "WarArrangementDetail": {"type": "object", "description": "战事安排详情", "properties": {"warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "isMobile": {"type": "boolean", "description": "是否为机动人员"}, "warGroup": {"$ref": "#/components/schemas/WarGroupInfo", "description": "战事分组信息（如果不是机动人员）"}}}, "WarGroupInfo": {"type": "object", "description": "战事分组信息", "properties": {"groupId": {"type": "string", "description": "分组ID"}, "groupName": {"type": "string", "description": "分组名称"}, "groupTask": {"type": "string", "description": "分组任务"}, "members": {"type": "array", "description": "分组成员列表", "items": {"$ref": "#/components/schemas/GameAccount"}}}}, "UpdateGameAccountRequest": {"type": "object", "description": "更新游戏账号请求", "properties": {"accountName": {"type": "string", "description": "账号名称"}, "powerValue": {"type": "string", "description": "战力值"}, "damageBonus": {"type": "number", "description": "伤害加成", "maximum": 999.99, "minimum": 0}, "troopLevel": {"type": "integer", "format": "int32", "description": "兵等级", "maximum": 30, "minimum": 1}, "rallyCapacity": {"type": "integer", "format": "int32", "description": "集结容量（万）"}, "troopQuantity": {"type": "string", "description": "兵量（万）"}, "lvbuStarLevel": {"type": "number", "description": "吕布星级（0-5星，遵循0.5进1规则）", "example": 0}, "memberTier": {"type": "string", "description": "成员等级", "enum": ["TIER_1", "TIER_2", "TIER_3", "TIER_4", "TIER_5"]}}}, "ApiResponseGameAccount": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/GameAccount", "description": "响应数据"}}}, "UpdateDynastyRequest": {"type": "object", "description": "更新王朝请求", "properties": {"name": {"type": "string", "description": "王朝名称", "example": "大汉王朝", "minLength": 1}}, "required": ["name"]}, "ApiResponseDynasty": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/Dynasty", "description": "响应数据"}}}, "UpdateArenaNotificationRequest": {"type": "object", "description": "更新演武场通知设置请求", "properties": {"enabled": {"type": "boolean", "description": "是否接收演武场通知", "example": true}}, "required": ["enabled"]}, "ApiResponseUser": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/User", "description": "响应数据"}}}, "UpdateAllianceRequest": {"type": "object", "description": "更新联盟请求", "properties": {"name": {"type": "string", "description": "联盟名称"}, "code": {"type": "string", "description": "联盟编码"}}}, "ApiResponseAlliance": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/Alliance", "description": "响应数据"}}}, "UpdateWarLimitsRequest": {"type": "object", "description": "更新官渡战事人数上限请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "guanduOneLimit": {"type": "integer", "format": "int32", "description": "官渡一人数上限", "example": 40, "maximum": 200, "minimum": 1}, "guanduTwoLimit": {"type": "integer", "format": "int32", "description": "官渡二人数上限", "example": 40, "maximum": 200, "minimum": 1}}, "required": ["allianceId"]}, "UpdateAllianceApprovalSettingsRequest": {"type": "object", "description": "更新联盟审核设置请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "allianceJoinApprovalRequired": {"type": "boolean", "description": "加入联盟是否需要审核", "example": true}, "warJoinApprovalRequired": {"type": "boolean", "description": "申请参加官渡是否需要审核", "example": true}}, "required": ["allianceId"]}, "UpdateNotificationSettingsRequest": {"type": "object", "description": "更新通知设置请求", "properties": {"accountId": {"type": "string", "description": "游戏账号ID", "example": 1}, "notificationTypes": {"type": "array", "description": "接收的通知类型列表", "items": {"type": "string", "enum": ["SAN_YING_ZHAN_LV_BU", "GUAN_DU_BAO_MING", "ZHU_JIU_LUN_YING_XIONG", "NAN_MAN_RU_QIN", "GONG_CHENG", "SHOU_CHENG", "SHUA_GONG_XUN", "YAN_WU_CHANG"]}, "uniqueItems": true}}, "required": ["accountId"]}, "UseTacticRequest": {"type": "object", "properties": {"warType": {"type": "string", "description": "战术使用在哪一种战事类型上", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"], "example": "GUANDU_ONE"}, "tactic": {"type": "string", "description": "战术类型", "enum": ["HOLD_GRAIN", "CUT_SUPPLY", "MULTI_ATTACK", "STEAL_POINT"], "example": "HOLD_GRAIN"}}, "required": ["tactic", "warType"]}, "ApiResponseObject": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"description": "响应数据"}}}, "ApiResponseWarArrangement": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/WarArrangement", "description": "响应数据"}}}, "WarArrangement": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "accountId": {"type": "string"}, "allianceId": {"type": "string"}, "warType": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "warGroupId": {"type": "string"}, "account": {"$ref": "#/components/schemas/GameAccount"}, "alliance": {"$ref": "#/components/schemas/Alliance"}, "warGroup": {"$ref": "#/components/schemas/WarGroup"}}}, "WarRequest": {"type": "object", "description": "战事请求", "properties": {"accountId": {"type": "string", "description": "账号ID"}, "warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}}, "required": ["accountId", "warType"]}, "ApiResponseWarApplication": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/WarApplication", "description": "响应数据"}}}, "WarApplication": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "accountId": {"type": "string"}, "allianceId": {"type": "string"}, "warType": {"type": "string", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"]}, "processedBy": {"type": "string"}, "account": {"$ref": "#/components/schemas/GameAccount"}, "alliance": {"$ref": "#/components/schemas/Alliance"}, "processor": {"$ref": "#/components/schemas/User"}}}, "ProcessApplicationRequest": {"type": "object", "description": "处理申请请求", "properties": {"approved": {"type": "boolean", "description": "是否通过申请"}}, "required": ["approved"]}, "CreateWarGroupRequest": {"type": "object", "description": "创建战事分组请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID"}, "warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "groupName": {"type": "string", "description": "分组名称", "minLength": 1}, "groupTask": {"type": "string", "description": "分组任务"}}, "required": ["allianceId", "groupName", "warType"]}, "ClearWarArrangementsRequest": {"type": "object", "description": "清空战事安排请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "sendNotification": {"type": "boolean", "description": "是否发送官渡报名通知", "example": false}, "notificationRemark": {"type": "string", "description": "通知备注（发送通知时使用）", "example": "官渡战事重新开放报名，请及时申请参加"}}, "required": ["allianceId", "warType"]}, "ArrangeMemberRequest": {"type": "object", "description": "安排成员请求", "properties": {"accountId": {"type": "string", "description": "账号ID"}, "warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "warGroupId": {"type": "string", "description": "战事分组ID（为空表示机动人员）"}}, "required": ["accountId", "warType"]}, "ApiResponseBoolean": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "boolean", "description": "响应数据"}}}, "CreateGameAccountRequest": {"type": "object", "description": "创建游戏账号请求", "properties": {"serverId": {"type": "integer", "format": "int32", "description": "区号"}, "accountName": {"type": "string", "description": "账号名称", "minLength": 1}, "powerValue": {"type": "string", "description": "战力值"}, "damageBonus": {"type": "number", "description": "伤害加成", "maximum": 999.99, "minimum": 0}, "troopLevel": {"type": "integer", "format": "int32", "description": "兵等级", "maximum": 30, "minimum": 1}, "rallyCapacity": {"type": "integer", "format": "int32", "description": "集结容量（万）"}, "troopQuantity": {"type": "string", "description": "兵量（万）"}, "lvbuStarLevel": {"type": "number", "default": "0.0", "description": "吕布星级（0-5星，遵循0.5进1规则）", "example": 0}}, "required": ["accountName", "powerValue", "serverId"]}, "JoinDynastyRequest": {"type": "object", "description": "加入王朝请求", "properties": {"dynastyCode": {"type": "string", "description": "王朝编码", "example": "ABC123", "minLength": 1, "pattern": "^[A-Za-z0-9]{6}$"}}, "required": ["dynastyCode"]}, "CreateDynastyRequest": {"type": "object", "description": "创建王朝请求", "properties": {"name": {"type": "string", "description": "王朝名称", "example": "大汉王朝", "minLength": 1}, "serverId": {"type": "integer", "format": "int32", "description": "区号", "example": 1}}, "required": ["name", "serverId"]}, "GrabPositionRequest": {"type": "object", "description": "抢夺官职请求", "properties": {"positionType": {"type": "string", "description": "官职类型", "enum": ["TAI_WEI", "SHANG_SHU_LING"], "example": "TAI_WEI"}, "timeSlot": {"type": "integer", "format": "int32", "description": "时段（0-23，表示00:00-00:59到23:00-23:59）", "example": 10, "maximum": 23, "minimum": 0}}, "required": ["positionType", "timeSlot"]}, "ApiResponsePositionGrab": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/PositionGrab", "description": "响应数据"}}}, "PositionGrab": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "dynastyId": {"type": "string"}, "positionType": {"type": "string", "enum": ["TAI_WEI", "SHANG_SHU_LING"]}, "dutyDate": {"type": "string", "format": "date"}, "timeSlot": {"type": "integer", "format": "int32"}, "accountId": {"type": "string"}, "dynasty": {"$ref": "#/components/schemas/Dynasty"}, "account": {"$ref": "#/components/schemas/GameAccount"}}}, "SetPositionGrabTimeRequest": {"type": "object", "description": "设置官职抢夺时间请求", "properties": {"positionType": {"type": "string", "description": "官职类型", "enum": ["TAI_WEI", "SHANG_SHU_LING"], "example": "TAI_WEI"}, "grabStartTime": {"type": "string", "format": "date-time", "description": "抢夺开始时间", "example": "2025/08/11 10:00:00"}, "grabEndTime": {"type": "string", "format": "date-time", "description": "抢夺结束时间", "example": "2025/08/11 23:00:00"}, "dutyDate": {"type": "string", "format": "date", "description": "任职日期", "example": "2025/08/12"}, "disabledTimeSlots": {"type": "array", "description": "禁用的时段列表（0-23）", "example": [0, 1, 2], "items": {"type": "integer", "format": "int32"}}}, "required": ["dutyDate", "grabEndTime", "grabStartTime", "positionType"]}, "ApiResponseDynastyPosition": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/DynastyPosition", "description": "响应数据"}}}, "DynastyPosition": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "dynastyId": {"type": "string"}, "positionType": {"type": "string", "enum": ["TAI_WEI", "SHANG_SHU_LING"]}, "grabStartTime": {"type": "string", "format": "date-time"}, "grabEndTime": {"type": "string", "format": "date-time"}, "dutyDate": {"type": "string", "format": "date"}, "disabledTimeSlots": {"type": "string"}, "dynasty": {"$ref": "#/components/schemas/Dynasty"}}}, "CreateBarbarianGroupRequest": {"type": "object", "description": "创建南蛮分组请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "groupName": {"type": "string", "description": "分组名称", "example": "第一小队", "minLength": 1}, "queueCount": {"type": "integer", "format": "int32", "description": "队列数量", "example": 3, "maximum": 6, "minimum": 1}}, "required": ["allianceId", "groupName", "queueCount"]}, "ApiResponseBarbarianGroup": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/BarbarianGroup", "description": "响应数据"}}}, "BarbarianGroup": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "allianceId": {"type": "string"}, "groupName": {"type": "string"}, "queueCount": {"type": "integer", "format": "int32"}}}, "JoinBarbarianGroupRequest": {"type": "object", "description": "加入南蛮分组请求", "properties": {"groupId": {"type": "string", "description": "分组ID", "example": 1}, "accountId": {"type": "string", "description": "账号ID", "example": 1}}, "required": ["accountId", "groupId"]}, "CreateAndJoinBarbarianGroupRequest": {"type": "object", "description": "创建并加入南蛮分组请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "groupName": {"type": "string", "description": "分组名称", "example": "第一小队", "minLength": 1}, "queueCount": {"type": "integer", "format": "int32", "description": "队列数量", "example": 3, "maximum": 6, "minimum": 1}, "accountId": {"type": "string", "description": "要加入分组的账号ID", "example": 1}}, "required": ["accountId", "allianceId", "groupName", "queueCount"]}, "LoginRequest": {"type": "object", "description": "登录请求", "properties": {"code": {"type": "string", "description": "微信小程序登录凭证", "minLength": 1}, "nickname": {"type": "string", "description": "用户昵称"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}}, "required": ["code"]}, "ApiResponseLoginResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/LoginResponse", "description": "响应数据"}}}, "LoginResponse": {"type": "object", "description": "登录响应", "properties": {"token": {"type": "string", "description": "JWT令牌"}, "userId": {"type": "string", "description": "用户ID"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "isNewUser": {"type": "boolean", "description": "是否为新用户"}}}, "CreateAllianceRequest": {"type": "object", "description": "创建联盟请求", "properties": {"name": {"type": "string", "description": "联盟名称", "minLength": 1}, "serverId": {"type": "integer", "format": "int32", "description": "区号"}}, "required": ["name", "serverId"]}, "TransferAllianceRequest": {"type": "object", "description": "转交联盟请求", "properties": {"newLeaderId": {"type": "string", "description": "新盟主用户ID"}}, "required": ["newLeaderId"]}, "SendAllianceNotificationRequest": {"type": "object", "description": "发送联盟活动通知请求", "properties": {"allianceId": {"type": "string", "description": "联盟ID", "example": 1}, "activityType": {"type": "string", "description": "活动类型", "enum": ["SAN_YING_ZHAN_LV_BU", "GUAN_DU_BAO_MING", "ZHU_JIU_LUN_YING_XIONG", "NAN_MAN_RU_QIN", "GONG_CHENG", "SHOU_CHENG", "SHUA_GONG_XUN", "YAN_WU_CHANG"]}, "startTime": {"type": "string", "format": "date-time", "description": "活动开始时间", "example": "2024/01/01 20:00:00"}, "remark": {"type": "string", "description": "备注内容", "example": "活动时间为活动预计开启时间，如有变更，盟主将另行通知", "maxLength": 200, "minLength": 0}}, "required": ["activityType", "allianceId", "startTime"]}, "JoinAllianceRequest": {"type": "object", "description": "申请加入联盟请求", "properties": {"accountId": {"type": "string", "description": "账号ID"}, "allianceCode": {"type": "string", "description": "联盟编码", "minLength": 1}}, "required": ["accountId", "allianceCode"]}, "AllianceApplication": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "accountId": {"type": "string"}, "allianceId": {"type": "string"}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"]}, "processedBy": {"type": "string"}, "account": {"$ref": "#/components/schemas/GameAccount"}, "alliance": {"$ref": "#/components/schemas/Alliance"}, "processor": {"$ref": "#/components/schemas/User"}}}, "ApiResponseAllianceApplication": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/AllianceApplication", "description": "响应数据"}}}, "ApiResponseListWarApplication": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/WarApplication"}}}}, "ApiResponseWarLimitStatusResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/WarLimitStatusResponse", "description": "响应数据"}}}, "WarLimitStatusResponse": {"type": "object", "description": "官渡战事人数上限状态响应", "properties": {"guanduOneLimit": {"type": "integer", "format": "int32", "description": "官渡一人数上限"}, "guanduOneArranged": {"type": "string", "description": "官渡一当前已安排人数"}, "guanduOnePending": {"type": "string", "description": "官渡一当前待处理申请人数"}, "guanduOneTotal": {"type": "string", "description": "官渡一总人数"}, "guanduOneFull": {"type": "boolean", "description": "官渡一是否已满"}, "guanduTwoLimit": {"type": "integer", "format": "int32", "description": "官渡二人数上限"}, "guanduTwoArranged": {"type": "string", "description": "官渡二当前已安排人数"}, "guanduTwoPending": {"type": "string", "description": "官渡二当前待处理申请人数"}, "guanduTwoTotal": {"type": "string", "description": "官渡二总人数"}, "guanduTwoFull": {"type": "boolean", "description": "官渡二是否已满"}}}, "ApiResponseListWarGroup": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/WarGroup"}}}}, "ApiResponseListWarArrangement": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/WarArrangement"}}}}, "ApiResponseWarArrangementResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/WarArrangementResponse", "description": "响应数据"}}}, "GameAccountWithApplicationTime": {"type": "object", "description": "游戏账号信息（包含申请时间）", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "userId": {"type": "string"}, "serverId": {"type": "integer", "format": "int32"}, "accountName": {"type": "string"}, "powerValue": {"type": "string"}, "damageBonus": {"type": "number"}, "troopLevel": {"type": "integer", "format": "int32"}, "rallyCapacity": {"type": "integer", "format": "int32"}, "troopQuantity": {"type": "string"}, "lvbuStarLevel": {"type": "number"}, "allianceId": {"type": "string"}, "dynastyId": {"type": "string"}, "barbarianGroupId": {"type": "string"}, "memberTier": {"type": "string", "enum": ["TIER_1", "TIER_2", "TIER_3", "TIER_4", "TIER_5"]}, "notificationTypes": {"type": "array", "items": {"type": "string", "enum": ["SAN_YING_ZHAN_LV_BU", "GUAN_DU_BAO_MING", "ZHU_JIU_LUN_YING_XIONG", "NAN_MAN_RU_QIN", "GONG_CHENG", "SHOU_CHENG", "SHUA_GONG_XUN", "YAN_WU_CHANG"]}, "uniqueItems": true}, "user": {"$ref": "#/components/schemas/User"}, "alliance": {"$ref": "#/components/schemas/Alliance"}, "dynasty": {"$ref": "#/components/schemas/Dynasty"}, "applicationTime": {"type": "string", "format": "date-time", "description": "申请时间（仅官渡战事有效）"}}, "required": ["lvbuStarLevel"]}, "WarArrangementResponse": {"type": "object", "description": "战事人员安排响应", "properties": {"allianceId": {"type": "string", "description": "联盟ID"}, "warType": {"type": "string", "description": "战事类型", "enum": ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"]}, "mobileMembers": {"type": "array", "description": "机动人员列表", "items": {"$ref": "#/components/schemas/GameAccountWithApplicationTime"}}, "warGroups": {"type": "array", "description": "战事分组列表", "items": {"$ref": "#/components/schemas/WarGroupDetail"}}}}, "WarGroupDetail": {"type": "object", "description": "战事分组详情", "properties": {"groupId": {"type": "string", "description": "分组ID"}, "groupName": {"type": "string", "description": "分组名称"}, "groupTask": {"type": "string", "description": "分组任务"}, "members": {"type": "array", "description": "分组成员列表", "items": {"$ref": "#/components/schemas/GameAccountWithApplicationTime"}}}}, "ApiResponseMapStringListWarApplication": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/WarApplication"}}, "description": "响应数据"}}}, "AllianceDetailResponse": {"type": "object", "description": "联盟详情响应", "properties": {"alliance": {"$ref": "#/components/schemas/Alliance", "description": "联盟基本信息"}, "members": {"type": "array", "description": "联盟成员列表", "items": {"$ref": "#/components/schemas/GameAccount"}}, "warGroups": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/WarGroup"}}, "description": "战事分组列表（按战事类型分组）"}, "warArrangements": {"type": "array", "description": "战事人员安排列表", "items": {"$ref": "#/components/schemas/WarArrangement"}}}}, "ApiResponseAllianceDetailResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/AllianceDetailResponse", "description": "响应数据"}}}, "ApiResponseListAllianceApplication": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/AllianceApplication"}}}}, "ApiResponseInteger": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "integer", "format": "int32", "description": "响应数据"}}}, "ApiResponseListBigDecimal": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"type": "number"}}}}, "ApiResponseListGameAccount": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/GameAccount"}}}}, "ApiResponseDynastyDetailResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/DynastyDetailResponse", "description": "响应数据"}}}, "DynastyDetailResponse": {"type": "object", "description": "王朝详情响应", "properties": {"dynasty": {"$ref": "#/components/schemas/Dynasty", "description": "王朝基本信息"}, "positions": {"type": "array", "description": "官职配置列表", "items": {"$ref": "#/components/schemas/DynastyPosition"}}, "positionGrabs": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PositionGrab"}}, "description": "官职抢夺结果（按官职类型分组）"}}}, "ApiResponsePositionGrabResultResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/PositionGrabResultResponse", "description": "响应数据"}}}, "PositionGrabResultResponse": {"type": "object", "description": "官职抢夺结果响应", "properties": {"dynastyId": {"type": "string", "description": "王朝ID"}, "dutyDate": {"type": "string", "format": "date", "description": "任职日期"}, "grabResults": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PositionGrab"}}, "description": "官职抢夺结果（按官职类型分组）"}, "availableTimeSlots": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "description": "可用时段信息（按官职类型分组）"}}}, "ApiResponseListDynasty": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/Dynasty"}}}}, "ApiResponseBarbarianGroupDetailResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/BarbarianGroupDetailResponse", "description": "响应数据"}}}, "BarbarianGroupDetailResponse": {"type": "object", "description": "南蛮分组详情响应", "properties": {"group": {"$ref": "#/components/schemas/BarbarianGroupResponse", "description": "分组基本信息"}, "members": {"type": "array", "description": "分组成员列表", "items": {"$ref": "#/components/schemas/GameAccount"}}, "memberCount": {"type": "integer", "format": "int32", "description": "成员数量"}}}, "BarbarianGroupResponse": {"type": "object", "description": "南蛮分组响应", "properties": {"id": {"type": "string", "description": "分组ID"}, "allianceId": {"type": "string", "description": "联盟ID"}, "groupName": {"type": "string", "description": "分组名称"}, "queueCount": {"type": "integer", "format": "int32", "description": "队列数量"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}, "ApiResponseListBarbarianGroupResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/BarbarianGroupResponse"}}}}, "ApiResponseBarbarianGroupResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/BarbarianGroupResponse", "description": "响应数据"}}}, "ApiResponseUserInfoResponse": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"$ref": "#/components/schemas/UserInfoResponse", "description": "响应数据"}}}, "UserInfoResponse": {"type": "object", "description": "用户信息响应", "properties": {"user": {"$ref": "#/components/schemas/User", "description": "用户基本信息"}, "alliances": {"type": "array", "description": "用户创建的联盟列表", "items": {"$ref": "#/components/schemas/Alliance"}}, "dynasties": {"type": "array", "description": "用户创建的王朝列表", "items": {"$ref": "#/components/schemas/Dynasty"}}, "gameAccounts": {"type": "array", "description": "用户的游戏账号列表", "items": {"$ref": "#/components/schemas/GameAccount"}}}}, "ApiResponseListAlliance": {"type": "object", "description": "API响应", "properties": {"code": {"type": "integer", "format": "int32", "description": "响应码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/Alliance"}}}}}, "securitySchemes": {"Bearer Authentication": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}