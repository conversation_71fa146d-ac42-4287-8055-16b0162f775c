// 环境配置
export interface EnvConfig {
  apiBaseUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
}

// 获取当前环境配置
const getEnvConfig = (): EnvConfig => {
  // 获取环境变量
  const nodeEnv = process.env.NODE_ENV;

  console.log("Environment detection:", {
    NODE_ENV: nodeEnv,
  });

  // 根据 NODE_ENV 判断环境
  const isProduction = nodeEnv === "production";
  const isTest = nodeEnv === "test";
  const isDevelopment = !isProduction && !isTest;

  let apiBaseUrl: string;

  if (isProduction) {
    apiBaseUrl = "https://weapp.linmt.cn/api";
  } else if (isTest) {
    apiBaseUrl =
      "https://game-hub-180609-4-1374650229.sh.run.tcloudbase.com/api";
  } else {
    // 默认开发环境
    apiBaseUrl = "http://localhost:8080/api";
  }

  const config: EnvConfig = {
    apiBaseUrl,
    isDevelopment,
    isProduction,
    isTest,
  };

  console.log("Final environment config:", config);

  return config;
};

export const envConfig = getEnvConfig();
