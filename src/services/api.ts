import { request } from "@/utils/request";
import {
  LoginRequest,
  LoginResponse,
  User,
  GameAccount,
  AccountForm,
  Alliance,
  AllianceForm,
  AllianceApplication,
  BattleApplication,
  BattleGroup,
  BattleGroupForm,
  BattleArrangement,
  UserSummary,
  AllianceDetail,
  BattleType,
  ApiResponse,
  WarArrangementResponse,
  AccountWarArrangementResponse,
  Dynasty,
  DynastyDetailResponse,
  PositionGrabResultResponse,
  CreateDynastyRequest,
  UpdateDynastyRequest,
  JoinDynastyRequest,
  GrabPositionRequest,
  SetPositionGrabTimeRequest,
  UseTacticRequest,
} from "@/types";

// 认证相关 API
export const authApi = {
  // 微信小程序登录
  login: (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return request.post("/auth/login", data);
  },

  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<User>> => {
    return request.get("/auth/user-info");
  },

  // 获取用户完整信息（包含联盟和账号）
  getUserCompleteInfo: (): Promise<ApiResponse<UserSummary>> => {
    return request.get("/auth/user-complete-info");
  },
};

// 游戏账号相关 API
export const accountApi = {
  // 创建账号
  create: (data: AccountForm): Promise<ApiResponse<GameAccount>> => {
    return request.post("/game-accounts", data);
  },

  // 更新账号
  update: (
    id: string,
    data: Partial<AccountForm>
  ): Promise<ApiResponse<GameAccount>> => {
    return request.put(`/game-accounts/${id}`, data);
  },

  // 更新游戏账号（包含成员等级）
  updateWithTier: (
    id: string,
    data: any
  ): Promise<ApiResponse<GameAccount>> => {
    return request.put(`/game-accounts/${id}`, data);
  },

  // 删除账号
  delete: (id: string): Promise<ApiResponse<void>> => {
    return request.delete(`/game-accounts/${id}`);
  },

  // 获取账号详情
  getById: (id: string): Promise<ApiResponse<GameAccount>> => {
    return request.get(`/game-accounts/${id}`);
  },

  // 获取用户的所有账号
  getMyAccounts: (): Promise<ApiResponse<GameAccount[]>> => {
    return request.get("/game-accounts/my");
  },

  // 获取联盟成员列表
  getAllianceMembers: (
    allianceId: string
  ): Promise<ApiResponse<GameAccount[]>> => {
    return request.get(`/game-accounts/alliance/${allianceId}`);
  },
};

// 联盟相关 API
export const allianceApi = {
  // 创建联盟
  create: (data: AllianceForm): Promise<ApiResponse<Alliance>> => {
    return request.post("/alliances", data);
  },

  // 更新联盟
  update: (
    id: string,
    data: Partial<AllianceForm>
  ): Promise<ApiResponse<Alliance>> => {
    return request.put(`/alliances/${id}`, data);
  },

  // 删除联盟
  delete: (id: string): Promise<ApiResponse<void>> => {
    return request.delete(`/alliances/${id}`);
  },

  // 转交联盟
  transfer: (
    id: string,
    newLeaderId: string
  ): Promise<ApiResponse<Alliance>> => {
    return request.post(`/alliances/${id}/transfer`, { newLeaderId });
  },

  // 获取联盟详情
  getById: (id: string): Promise<ApiResponse<Alliance>> => {
    return request.get(`/alliances/${id}`);
  },

  // 获取联盟完整详情（包含成员和战事安排）
  getDetail: (id: string): Promise<ApiResponse<AllianceDetail>> => {
    return request.get(`/query/alliances/${id}/detail`);
  },

  // 获取用户创建的联盟
  getMyAlliances: (): Promise<ApiResponse<Alliance[]>> => {
    return request.get("/alliances/my");
  },

  // 通过联盟编码查找联盟
  getByCode: (code: string): Promise<ApiResponse<Alliance>> => {
    return request.get(`/alliances/code/${code}`);
  },

  // 获取联盟成员列表
  getMembers: (allianceId: string): Promise<ApiResponse<GameAccount[]>> => {
    return request.get(`/alliance-members/alliances/${allianceId}/members`);
  },

  // 更新联盟审核设置
  updateApprovalSettings: (data: {
    allianceId: string;
    allianceJoinApprovalRequired?: boolean;
    warJoinApprovalRequired?: boolean;
  }): Promise<ApiResponse<Alliance>> => {
    return request.put("/alliances/approval-settings", data);
  },

  // 更新官渡战事人数上限
  updateWarLimits: (data: {
    allianceId: string;
    guanduOneLimit: number;
    guanduTwoLimit: number;
  }): Promise<ApiResponse<void>> => {
    return request.put("/alliances/war-limits", data);
  },

  // 获取联盟官渡战事人数上限状态
  getWarLimitStatus: (allianceId: string): Promise<ApiResponse<any>> => {
    return request.get(`/wars/alliance/${allianceId}/limit-status`);
  },
};

// 联盟申请相关 API
export const allianceApplicationApi = {
  // 申请加入联盟
  apply: (data: {
    allianceCode: string;
    accountId: string;
  }): Promise<ApiResponse<AllianceApplication>> => {
    return request.post("/alliance-members/apply", data);
  },

  // 处理申请（通过/拒绝）
  process: (
    id: string,
    approved: boolean
  ): Promise<ApiResponse<AllianceApplication>> => {
    return request.post(`/alliance-members/applications/${id}/process`, {
      approved,
    });
  },

  // 获取联盟的申请列表
  getByAlliance: (
    allianceId: string
  ): Promise<ApiResponse<AllianceApplication[]>> => {
    return request.get(
      `/alliance-members/alliances/${allianceId}/applications`
    );
  },

  // 获取账号的申请状态
  getByAccount: (
    accountId: string
  ): Promise<ApiResponse<AllianceApplication[]>> => {
    return request.get(`/alliance-members/accounts/${accountId}/applications`);
  },
};

// 联盟成员相关 API
export const allianceMemberApi = {
  // 获取联盟成员列表
  getMembers: (allianceId: string): Promise<ApiResponse<GameAccount[]>> => {
    return request.get(`/alliance-members/alliances/${allianceId}/members`);
  },

  // 移除成员
  remove: (accountId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/alliance-members/accounts/${accountId}`);
  },

  // 移除联盟成员
  removeMember: (accountId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/alliance-members/accounts/${accountId}`);
  },
};

// 战事申请相关 API
export const battleApplicationApi = {
  // 申请参加战事
  apply: (data: {
    allianceId: string;
    accountId: string;
    warType: BattleType;
  }): Promise<ApiResponse<BattleApplication>> => {
    return request.post("/wars/apply", data);
  },

  // 处理申请
  process: (
    id: string,
    approved: boolean
  ): Promise<ApiResponse<BattleApplication>> => {
    return request.post(`/wars/applications/${id}/process`, { approved });
  },

  // 移动申请到其他战事
  move: (
    id: string,
    newWarType: BattleType
  ): Promise<ApiResponse<BattleApplication>> => {
    return request.post(`/wars/applications/${id}/move`, {
      warType: newWarType,
    });
  },

  // 获取联盟的战事申请列表
  getByAlliance: (
    allianceId: string,
    warType: BattleType
  ): Promise<ApiResponse<BattleApplication[]>> => {
    return request.get(`/wars/alliances/${allianceId}/applications`, {
      warType,
    });
  },

  // 获取账号的战事申请状态
  getByAccount: (
    accountId: string
  ): Promise<ApiResponse<BattleApplication[]>> => {
    return request.get(`/wars/accounts/${accountId}/applications`);
  },
};

// 战事分组相关 API
export const battleGroupApi = {
  // 创建分组
  create: (data: BattleGroupForm): Promise<ApiResponse<BattleGroup>> => {
    return request.post("/war-groups", data);
  },

  // 更新分组
  update: (
    id: string,
    data: Partial<BattleGroupForm>
  ): Promise<ApiResponse<BattleGroup>> => {
    return request.put(`/war-groups/${id}`, data);
  },

  // 删除分组
  delete: (id: string): Promise<ApiResponse<void>> => {
    return request.delete(`/war-groups/${id}`);
  },

  // 获取联盟的战事分组
  getByAlliance: (
    allianceId: string,
    warType: BattleType
  ): Promise<ApiResponse<BattleGroup[]>> => {
    return request.get(`/war-groups/alliances/${allianceId}`, { warType });
  },
};

// 战事安排相关 API
export const battleArrangementApi = {
  // 安排成员到战事
  assign: (data: {
    allianceId: string;
    accountId: string;
    warType: BattleType;
    groupId?: string;
  }): Promise<ApiResponse<BattleArrangement>> => {
    return request.post("/war-groups/arrange", data);
  },

  // 清空战事安排
  clear: (
    allianceId: string,
    warType: BattleType
  ): Promise<ApiResponse<void>> => {
    return request.delete(`/war-groups/alliances/${allianceId}/arrangements`, {
      warType,
    });
  },

  // 获取战事安排
  getByBattle: (
    allianceId: string,
    warType?: BattleType
  ): Promise<ApiResponse<BattleArrangement[]>> => {
    const params = warType ? { warType } : {};
    return request.get(
      `/war-groups/alliances/${allianceId}/arrangements`,
      params
    );
  },
};

// 查询相关 API
export const queryApi = {
  // 获取联盟战事申请列表
  getAllianceWarApplications: (
    allianceId: string
  ): Promise<ApiResponse<any>> => {
    return request.get(`/query/alliances/${allianceId}/war-applications`);
  },

  // 获取账号战事申请状态
  getAccountWarApplications: (
    accountId: string
  ): Promise<ApiResponse<BattleApplication[]>> => {
    return request.get(`/query/accounts/${accountId}/war-applications`);
  },

  // 获取账号联盟申请状态
  getAccountAllianceApplications: (
    accountId: string
  ): Promise<ApiResponse<AllianceApplication[]>> => {
    return request.get(`/query/accounts/${accountId}/alliance-applications`);
  },
};

// 战事管理 API (基于联盟)
export const battleApi = {
  // 申请参加战事
  apply: (data: {
    accountId: string;
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  }): Promise<ApiResponse<BattleApplication>> => {
    return request.post("/wars/apply", data);
  },

  // 处理战事申请
  processApplication: (
    applicationId: string,
    approved: boolean
  ): Promise<ApiResponse<BattleApplication>> => {
    return request.post(`/wars/applications/${applicationId}/process`, {
      approved,
    });
  },

  // 调动成员参加的战事（官渡一与官渡二之间互相调动）
  moveWarApplication: (accountId: string): Promise<ApiResponse<any>> => {
    return request.post(`/wars/move-guan-du/${accountId}`);
  },

  // 盟主直接添加成员到战事中
  addToWar: (
    accountId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<any>> => {
    return request.post(`/wars/add-to-war/${accountId}?warType=${warType}`);
  },

  // 将成员移除出指定战事
  removeFromWar: (
    accountId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<any>> => {
    return request.post(
      `/wars/remove-from-war/${accountId}?warType=${warType}`
    );
  },

  // 取消申请参加战事
  cancelApplication: (
    accountId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<void>> => {
    return request.post("/wars/cancel-apply", { accountId, warType });
  },

  // 取消申请参加战事
  cancelWarApplication: (applicationId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/wars/applications/${applicationId}`);
  },

  // 移动战事申请到其他战事类型
  moveApplication: (
    applicationId: string,
    newWarType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<BattleApplication>> => {
    return request.post(`/wars/applications/${applicationId}/move`, {
      newWarType,
    });
  },

  // 获取联盟战事申请列表
  getAllianceApplications: (
    allianceId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<BattleApplication[]>> => {
    return request.get(
      `/wars/alliances/${allianceId}/applications?warType=${warType}`
    );
  },

  // 获取账号战事申请历史
  getAccountApplications: (
    accountId: string
  ): Promise<ApiResponse<BattleApplication[]>> => {
    return request.get(`/wars/accounts/${accountId}/applications`);
  },

  // 联盟使用预制战术
  useTactic: (
    allianceId: string,
    data: UseTacticRequest
  ): Promise<ApiResponse<void>> => {
    return request.post(`/wars/use-tactic/${allianceId}`, data);
  },
};

// 战事分组管理 API
export const warGroupApi = {
  // 创建战事分组
  create: (data: {
    allianceId: string;
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
    groupName: string;
    groupTask?: string;
  }): Promise<ApiResponse<any>> => {
    return request.post("/war-groups", data);
  },

  // 更新战事分组
  update: (
    groupId: string,
    data: { groupName: string; groupTask?: string }
  ): Promise<ApiResponse<any>> => {
    return request.put(`/war-groups/${groupId}`, data);
  },

  // 删除战事分组
  delete: (groupId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/war-groups/${groupId}`);
  },

  // 获取联盟战事分组列表
  getByAlliance: (
    allianceId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<any[]>> => {
    return request.get(
      `/war-groups/alliances/${allianceId}?warType=${warType}`
    );
  },

  // 安排成员到战事分组
  arrangeMember: (data: {
    accountId: string;
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
    warGroupId?: string | null;
  }): Promise<ApiResponse<any>> => {
    return request.post("/war-groups/arrange", data);
  },

  // 获取战事人员安排
  getArrangements: (
    allianceId: string,
    warType?: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<any[]>> => {
    const url = warType
      ? `/war-groups/alliances/${allianceId}/arrangements?warType=${warType}`
      : `/war-groups/alliances/${allianceId}/arrangements`;
    return request.get(url);
  },

  // 获取战事人员安排详情（包含分组和机动人员）
  getArrangementDetail: (
    allianceId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<WarArrangementResponse>> => {
    return request.get(
      `/war-groups/alliances/${allianceId}/arrangements/detail?warType=${warType}`
    );
  },

  // 获取账号的战事安排详情
  getAccountArrangementDetail: (
    accountId: string
  ): Promise<ApiResponse<AccountWarArrangementResponse>> => {
    return request.get(`/war-groups/accounts/${accountId}/arrangements`);
  },

  // 清空战事安排
  clearArrangements: (
    allianceId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ): Promise<ApiResponse<void>> => {
    return request.delete(
      `/war-groups/alliances/${allianceId}/arrangements?warType=${warType}`
    );
  },

  // 清空战事安排（支持可选通知）
  clearArrangementsWithNotification: (data: {
    allianceId: string;
    warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
    sendNotification?: boolean;
  }): Promise<ApiResponse<void>> => {
    return request.post("/war-groups/clear-arrangements", data);
  },
};

// 王朝相关 API
export const dynastyApi = {
  // 创建王朝
  create: (data: CreateDynastyRequest): Promise<ApiResponse<Dynasty>> => {
    return request.post("/dynasties", data);
  },

  // 获取我创建的王朝列表
  getMy: (): Promise<ApiResponse<Dynasty[]>> => {
    return request.get("/dynasties/my");
  },

  // 获取王朝详情
  getById: (dynastyId: string): Promise<ApiResponse<DynastyDetailResponse>> => {
    return request.get(`/dynasties/${dynastyId}`);
  },

  // 通过编码获取王朝信息
  getByCode: (code: string): Promise<ApiResponse<Dynasty>> => {
    return request.get(`/dynasties/code/${code}`);
  },

  // 更新王朝
  update: (
    dynastyId: string,
    data: UpdateDynastyRequest
  ): Promise<ApiResponse<Dynasty>> => {
    return request.put(`/dynasties/${dynastyId}`, data);
  },

  // 删除王朝
  delete: (dynastyId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/dynasties/${dynastyId}`);
  },

  // 开启或关闭官职抢夺
  toggleGrab: (
    dynastyId: string,
    enabled: boolean
  ): Promise<ApiResponse<Dynasty>> => {
    return request.post(
      `/dynasties/${dynastyId}/toggle-grab?enabled=${enabled}`
    );
  },

  // 清空所有抢夺结果
  clearAllGrabResults: (dynastyId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/dynasties/${dynastyId}/grab-results`);
  },

  // 清空指定官职的抢夺结果
  clearPositionGrabResults: (
    dynastyId: string,
    positionType: "TAI_WEI" | "SHANG_SHU_LING"
  ): Promise<ApiResponse<void>> => {
    return request.delete(
      `/dynasties/${dynastyId}/grab-results/${positionType}`
    );
  },
};

// 官职抢夺相关 API
export const positionApi = {
  // 设置官职抢夺时间
  setGrabTime: (
    dynastyId: string,
    data: SetPositionGrabTimeRequest
  ): Promise<ApiResponse<void>> => {
    return request.post(`/dynasties/${dynastyId}/positions/grab-time`, data);
  },

  // 抢夺官职
  grab: (
    dynastyId: string,
    accountId: string,
    data: GrabPositionRequest
  ): Promise<ApiResponse<void>> => {
    return request.post(
      `/dynasties/${dynastyId}/positions/grab?accountId=${accountId}`,
      data
    );
  },

  // 获取官职抢夺结果
  getGrabResults: (
    dynastyId: string,
    dutyDate: string
  ): Promise<ApiResponse<PositionGrabResultResponse>> => {
    return request.get(
      `/dynasties/${dynastyId}/positions/grab-results?dutyDate=${dutyDate}`
    );
  },
};

// 账号王朝相关 API
export const accountDynastyApi = {
  // 账号加入王朝
  join: (
    accountId: string,
    data: JoinDynastyRequest
  ): Promise<ApiResponse<void>> => {
    return request.post(`/game-accounts/${accountId}/join-dynasty`, data);
  },

  // 账号退出王朝
  leave: (accountId: string): Promise<ApiResponse<void>> => {
    return request.post(`/dynasties/accounts/${accountId}/leave`);
  },
};

// 消息订阅管理 API
export const messageSubscriptionApi = {
  // 获取当前可接收消息数量
  getSubscriptionCount: (): Promise<ApiResponse<number>> => {
    return request.get("/message-subscription/count");
  },

  // 增加消息订阅数量
  addSubscriptionCount: (): Promise<ApiResponse<User>> => {
    return request.post("/message-subscription/add");
  },

  // 清空订阅数量
  clearSubscription: (): Promise<ApiResponse<boolean>> => {
    return request.post("/message-subscription/clear");
  },

  // 检查是否有足够的消息订阅数量
  checkSubscription: (requiredCount: number): Promise<ApiResponse<boolean>> => {
    return request.get(`/message-subscription/check/${requiredCount}`);
  },
};

// 联盟通知管理 API
export const allianceNotificationApi = {
  // 更新账号通知设置
  updateNotificationSettings: (data: {
    accountId: string;
    notificationTypes: string[];
  }): Promise<ApiResponse<GameAccount>> => {
    return request.put("/alliance-notifications/settings", data);
  },

  // 发送联盟活动通知
  sendAllianceNotification: (data: {
    allianceId: string;
    activityType: string;
    startTime: string;
    remark?: string;
  }): Promise<ApiResponse<void>> => {
    return request.post("/alliance-notifications/send", data);
  },
};

// 演武场通知管理 API
export const arenaNotificationApi = {
  // 获取演武场通知设置
  getArenaNotificationSetting: (): Promise<ApiResponse<boolean>> => {
    return request.get("/arena-notifications/settings");
  },

  // 更新演武场通知设置
  updateArenaNotificationSetting: (data: {
    enabled: boolean;
  }): Promise<ApiResponse<void>> => {
    return request.put("/arena-notifications/settings", data);
  },
};

// 南蛮入侵分组管理 API
export const barbarianGroupApi = {
  // 创建南蛮分组（空分组）
  createBarbarianGroup: (data: {
    allianceId: string;
    groupName: string;
    queueCount: number;
    description?: string;
  }): Promise<ApiResponse<any>> => {
    return request.post("/barbarian-groups", data);
  },

  // 创建分组并立即加入
  createAndJoinBarbarianGroup: (data: {
    allianceId: string;
    groupName: string;
    queueCount: number;
    description?: string;
    accountId: string;
  }): Promise<ApiResponse<any>> => {
    return request.post("/barbarian-groups/create-and-join", data);
  },

  // 加入南蛮分组
  joinBarbarianGroup: (data: {
    groupId: string;
    accountId: string;
  }): Promise<ApiResponse<void>> => {
    return request.post("/barbarian-groups/join", data);
  },

  // 离开南蛮分组
  leaveBarbarianGroup: (accountId: string): Promise<ApiResponse<void>> => {
    return request.delete(`/barbarian-groups/leave/${accountId}`);
  },

  // 获取分组详情（包含成员信息）
  getBarbarianGroupDetail: (groupId: string): Promise<ApiResponse<any>> => {
    return request.get(`/barbarian-groups/${groupId}/detail`);
  },

  // 获取联盟的所有南蛮分组
  getAllianceBarbarianGroups: (
    allianceId: string
  ): Promise<ApiResponse<any[]>> => {
    return request.get(`/barbarian-groups/alliance/${allianceId}`);
  },

  // 根据队列数量查询联盟中的南蛮分组
  getBarbarianGroupsByQueueCount: (
    allianceId: string,
    queueCount: number
  ): Promise<ApiResponse<any[]>> => {
    return request.get(
      `/barbarian-groups/alliance/${allianceId}/queue/${queueCount}`
    );
  },

  // 获取账号所在的南蛮分组
  getAccountBarbarianGroup: (accountId: string): Promise<ApiResponse<any>> => {
    return request.get(`/barbarian-groups/account/${accountId}`);
  },
};
