import Taro from "@tarojs/taro";
import { BattleType, MemberTier } from "@/types";

/**
 * 格式化数字，添加千分位分隔符
 */
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

/**
 * 格式化战力值
 */
export const formatPower = (power: number | string | undefined): string => {
  if (!power) {
    return "-";
  }
  if (typeof power === "string") {
    power = parseInt(power);
  }
  if (power >= 100000000) {
    return `${(power / 100000000).toFixed(0)}亿`;
  } else if (power >= 10000) {
    return `${(power / 10000).toFixed(0)}万`;
  }
  return formatNumber(power);
};

/**
 * 格式化集结容量
 */
export const formatRallyCapacity = (rallyCapacity: number): string => {
  if (rallyCapacity >= 100000000) {
    return `${(rallyCapacity / 100000000).toFixed(1)}亿`;
  } else if (rallyCapacity >= 10000) {
    return `${(rallyCapacity / 10000).toFixed(1)}万`;
  }
  return formatNumber(rallyCapacity);
};

/**
 * 格式化时间
 */
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;

  if (diff < minute) {
    return "刚刚";
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < month) {
    return `${Math.floor(diff / day)}天前`;
  } else {
    return date.toLocaleDateString("zh-CN");
  }
};

/**
 * 格式化日期
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

/**
 * 格式化日期时间
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * 格式化日期时间为API请求格式 (yyyy/MM/dd HH:mm:ss)
 */
export const formatDateTimeForAPI = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化日期为API请求格式 (yyyy/MM/dd)
 */
export const formatDateForAPI = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}/${month}/${day}`;
};

/**
 * 获取本地日期字符串 (yyyy-MM-dd)
 * 解决 toISOString() 时区问题
 */
export const getLocalDateString = (date: Date = new Date()): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

/**
 * 获取战事类型显示名称
 */
export const getBattleTypeName = (battleType: BattleType): string => {
  const names = {
    [BattleType.GUANDU_ONE]: "官渡一",
    [BattleType.GUANDU_TWO]: "官渡二",
    [BattleType.SIEGE]: "攻城",
    [BattleType.DEFENSE]: "守城",
  };
  return names[battleType] || battleType;
};

/**
 * 获取成员等级显示名称
 */
export const getMemberTierName = (tier: MemberTier): string => {
  const names = {
    [MemberTier.TIER_1]: "一阶",
    [MemberTier.TIER_2]: "二阶",
    [MemberTier.TIER_3]: "三阶",
    [MemberTier.TIER_4]: "四阶",
    [MemberTier.TIER_5]: "五阶",
  };
  return names[tier] || `${tier}阶`;
};

/**
 * 获取申请状态显示名称
 */
export const getApplicationStatusName = (status: string): string => {
  const names = {
    PENDING: "待审核",
    APPROVED: "已通过",
    REJECTED: "已拒绝",
  };
  return names[status] || status;
};

/**
 * 获取申请状态颜色
 */
export const getApplicationStatusColor = (status: string): string => {
  const colors = {
    PENDING: "#ff9800",
    APPROVED: "#4caf50",
    REJECTED: "#f44336",
  };
  return colors[status] || "#757575";
};

/**
 * 验证表单数据
 */
export const validateForm = (
  data: any,
  rules: any
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  for (const field in rules) {
    const rule = rules[field];
    const value = data[field];

    if (rule.required && (!value || value === "")) {
      errors.push(rule.message || `${field}不能为空`);
      continue;
    }

    if (value && rule.min && value < rule.min) {
      errors.push(rule.message || `${field}不能小于${rule.min}`);
    }

    if (value && rule.max && value > rule.max) {
      errors.push(rule.message || `${field}不能大于${rule.max}`);
    }

    if (value && rule.pattern && !rule.pattern.test(value)) {
      errors.push(rule.message || `${field}格式不正确`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * 显示成功提示
 */
export const showSuccess = (title: string) => {
  Taro.showToast({
    title,
    icon: "none",
    duration: 2000,
  });
};

/**
 * 显示错误提示
 */
export const showError = (title: string) => {
  Taro.showToast({
    title,
    icon: "none",
    duration: 2000,
  });
};

/**
 * 显示加载提示
 */
export const showLoading = (title: string = "加载中...") => {
  Taro.showLoading({
    title,
    mask: true,
  });
};

/**
 * 隐藏加载提示
 */
export const hideLoading = () => {
  Taro.hideLoading();
};

/**
 * 确认对话框
 */
export const showConfirm = (
  content: string,
  title: string = "提示"
): Promise<boolean> => {
  return new Promise((resolve) => {
    Taro.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm);
      },
      fail: () => {
        resolve(false);
      },
    });
  });
};
