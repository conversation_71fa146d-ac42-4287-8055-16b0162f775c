import Taro from "@tarojs/taro";
import { envConfig } from "@/config/env";

const BASE_URL = envConfig.apiBaseUrl;
console.log("API BASE_URL:", BASE_URL, "Environment:", {
  isDevelopment: envConfig.isDevelopment,
  isProduction: envConfig.isProduction,
  isTest: envConfig.isTest,
});

// 统一处理响应结果
function handleResponse(res: Taro.request.SuccessCallbackResult<any>, url: string) {
  console.log(`HTTP ${res.statusCode} <-- ${url}`);
  console.log("响应数据:", res.data);

  if (res.statusCode === 200) {
    if (res.data.code !== 200) {
      const msg = res.data.message || "请求失败";
      Taro.showToast({ title: msg, icon: "none" });
      return Promise.reject(new Error(msg));
    }
    return res.data;
  }

  if (res.statusCode === 400 && res.data.code === 400 && res.data.message) {
    Taro.showToast({ title: res.data.message, icon: "none" });
    return Promise.reject(new Error(res.data.message));
  }

  if (res.statusCode === 401) {
    Taro.removeStorageSync("token");
    Taro.removeStorageSync("userInfo");
    Taro.redirectTo({ url: "/pages/login/index" });
    Taro.showToast({ title: "登录已过期，请重新登录", icon: "none" });
    return Promise.reject(new Error("登录已过期，请重新登录"));
  }

  Taro.showToast({ title: "网络请求失败", icon: "none" });
  return Promise.reject(new Error("网络请求失败"));
}

// 统一处理请求错误
function handleError(error: any) {
  console.error("请求错误:", error);
  if (error.message !== "登录已过期，请重新登录") {
    Taro.showToast({ title: error.message || "网络请求失败", icon: "none" });
  }
  return Promise.reject(error);
}

// 核心请求函数
async function api(
  method: "GET" | "POST" | "PUT" | "DELETE",
  url: string,
  data?: any,
  params?: any
) {
  let fullUrl = `${BASE_URL}${url}`;

  // 拼接 query 参数
  if (params && method === "POST") {
    const queryString = Object.keys(params)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join("&");
    fullUrl += `?${queryString}`;
  }

  console.log(`${method} 请求完整 URL:`, fullUrl);

  // 设置 headers
  const token = Taro.getStorageSync("token");
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };
  if (token) headers["Authorization"] = `Bearer ${token}`;

  console.log("请求头:", headers);
  console.log("请求数据/参数:", method === "GET" ? params : data);

  try {
    const res = await Taro.request({
      url: fullUrl,
      method,
      data: method === "GET" || method === "DELETE" ? params : data,
      header: headers,
    });
    return handleResponse(res, fullUrl);
  } catch (err) {
    return handleError(err);
  }
}

// 导出方法
export const request = {
  get: (url: string, params?: any) => api("GET", url, undefined, params),
  post: (url: string, data?: any, params?: any) => api("POST", url, data, params),
  put: (url: string, data?: any) => api("PUT", url, data),
  delete: (url: string, params?: any) => api("DELETE", url, undefined, params),
};

export default request;
