import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { Button, Switch, Cell } from "@taroify/core";
import {
  User,
  Plus,
  FireOutlined,
  ShieldOutlined,
  Star,
  Setting,
} from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { authApi, arenaNotificationApi } from "@/services/api";
import { UserSummary, Alliance, GameAccount } from "@/types";
import { showError, showSuccess, formatPower } from "@/utils";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import "./index.scss";

const SanguoGame: React.FC = () => {
  const { isLoggedIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [userSummary, setUserSummary] = useState<UserSummary | null>(null);
  const [arenaNotificationEnabled, setArenaNotificationEnabled] =
    useState(false);
  const [arenaNotificationLoading, setArenaNotificationLoading] =
    useState(false);

  useEffect(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadUserSummary();
      loadArenaNotificationSetting();
    } else {
      // 未登录用户，确保loading为false
      setLoading(false);
    }
  }, [isLoggedIn]);

  // 页面显示时刷新数据
  useDidShow(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadUserSummary();
      loadArenaNotificationSetting();
    } else {
      // 未登录用户，确保loading为false
      setLoading(false);
    }
  });

  const loadUserSummary = async () => {
    try {
      //setLoading(true);
      const response = await authApi.getUserCompleteInfo();
      console.log("用户完整信息响应:", response.data);
      console.log("联盟数据:", response.data.alliances);
      console.log("账号数据:", response.data.gameAccounts);
      setUserSummary(response.data);
    } catch (error) {
      console.error("加载用户数据失败:", error);
      showError("加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 加载演武场通知设置
  const loadArenaNotificationSetting = async () => {
    try {
      const response = await arenaNotificationApi.getArenaNotificationSetting();
      setArenaNotificationEnabled(response.data);
    } catch (error) {
      console.error("加载演武场通知设置失败:", error);
    }
  };

  // 更新演武场通知设置
  const handleArenaNotificationChange = async (enabled: boolean) => {
    try {
      setArenaNotificationLoading(true);
      await arenaNotificationApi.updateArenaNotificationSetting({ enabled });
      setArenaNotificationEnabled(enabled);
      showSuccess(enabled ? "已开启演武场通知" : "已关闭演武场通知");
    } catch (error) {
      console.error("更新演武场通知设置失败:", error);
      showError("更新设置失败");
    } finally {
      setArenaNotificationLoading(false);
    }
  };

  const handleCreateAccount = () => {
    // 创建账号需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/account/form",
    });
  };

  const handleCreateAlliance = () => {
    // 创建联盟需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/alliance/form",
    });
  };

  const handleAccountList = () => {
    // 未登录用户也可以查看账号列表页面（但不会有数据）
    Taro.navigateTo({
      url: "/pages/game/sanguo/account/list",
    });
  };

  const handleAllianceList = () => {
    // 未登录用户也可以查看联盟列表页面（但不会有数据）
    Taro.navigateTo({
      url: "/pages/game/sanguo/alliance/list",
    });
  };

  const handleCreateDynasty = () => {
    // 创建王朝需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/dynasty/form",
    });
  };

  const handleDynastyList = () => {
    // 未登录用户也可以查看王朝列表页面（但不会有数据）
    Taro.navigateTo({
      url: "/pages/game/sanguo/dynasty/list",
    });
  };

  const handleAllianceDetail = (alliance: Alliance) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/alliance/detail?id=${alliance.id}`,
    });
  };

  const handleAccountDetail = (account: GameAccount) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/account/detail?id=${account.id}`,
    });
  };

  const emptyStyle = {
    padding: 0,
  };

  return (
    <View className="sanguo-game-page">
      {/* 快速操作 */}
      {/* <View className="quick-actions">
        <View className="action-grid">
          <View className="action-item" onClick={handleAccountList}>
            <View className="action-icon">
              <User size="32rpx" color="#1976d2" />
            </View>
            <Text className="action-text">账号管理</Text>
          </View>
          <View className="action-item" onClick={handleAllianceList}>
            <View className="action-icon">
              <FireOutlined size="32rpx" color="#1976d2" />
            </View>
            <Text className="action-text">联盟管理</Text>
          </View>
          <View className="action-item" onClick={handleDynastyList}>
            <View className="action-icon">
              <Star size="32rpx" color="#1976d2" />
            </View>
            <Text className="action-text">王朝管理</Text>
          </View>
        </View>
      </View> */}

      {/* 我的王朝 */}
      <View className="section">
        <View className="section-header">
          <View className="section-title-wrapper">
            <Text className="section-title">我的王朝</Text>
            <Text className="section-count">
              ({userSummary?.dynasties?.length || 0})
            </Text>
          </View>
          <Button
            size="small"
            color="primary"
            icon={<Plus size="16rpx" />}
            onClick={handleCreateDynasty}
          >
            创建王朝
          </Button>
        </View>

        {loading ? (
          <LoadingState text="加载中..." />
        ) : !isLoggedIn ? (
          <EmptyState
            title="请先登录"
            description="登录后查看您的王朝信息"
            actionText="立即登录"
            onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
          />
        ) : userSummary?.dynasties && userSummary.dynasties.length > 0 ? (
          <View className="dynasty-list">
            {userSummary.dynasties.map((dynasty) => (
              <PageCard
                key={dynasty.id}
                title={dynasty.name}
                description={`编码: ${dynasty.code} · ${dynasty.serverId}区`}
                icon={<Star size="40rpx" color="#1976d2" />}
                extra={
                  <View className="dynasty-status">
                    <Text
                      className={`status-badge ${
                        dynasty.grabEnabled ? "enabled" : "disabled"
                      }`}
                    >
                      {dynasty.grabEnabled ? "抢夺开启" : "抢夺关闭"}
                    </Text>
                  </View>
                }
                onClick={() => {
                  Taro.navigateTo({
                    url: `/pages/game/sanguo/dynasty/detail?id=${dynasty.id}`,
                  });
                }}
              />
            ))}
          </View>
        ) : (
          <EmptyState
            title="暂无王朝"
            style={emptyStyle}
            description="创建王朝开启官职抢夺"
          />
        )}
      </View>

      {/* 我的联盟 */}
      <View className="section">
        <View className="section-header">
          <View className="section-title-wrapper">
            <Text className="section-title">我的联盟</Text>
            <Text className="section-count">
              ({userSummary?.alliances?.length || 0})
            </Text>
          </View>
          <Button
            size="small"
            color="primary"
            icon={<Plus size="16rpx" />}
            onClick={handleCreateAlliance}
          >
            创建联盟
          </Button>
        </View>

        {loading ? (
          <LoadingState text="加载中..." />
        ) : !isLoggedIn ? (
          <EmptyState
            title="请先登录"
            description="登录后查看您的联盟信息"
            actionText="立即登录"
            onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
          />
        ) : userSummary?.alliances && userSummary.alliances.length > 0 ? (
          <View className="alliance-list">
            {userSummary.alliances.map((alliance) => (
              <PageCard
                key={alliance.id}
                title={alliance.name}
                description={`${alliance.serverId}区 · 编码：${alliance.code}`}
                icon={<ShieldOutlined size="40rpx" color="#1976d2" />}
                onClick={() => handleAllianceDetail(alliance)}
              />
            ))}
          </View>
        ) : (
          <EmptyState
            title="暂无联盟"
            style={emptyStyle}
            description="创建联盟邀请盟友加入，安排战事"
          />
        )}
      </View>

      {/* 我的账号 */}
      <View className="section">
        <View className="section-header">
          <View className="section-title-wrapper">
            <Text className="section-title">我的账号</Text>
            <Text className="section-count">
              ({userSummary?.gameAccounts?.length || 0})
            </Text>
          </View>
          <Button
            size="small"
            color="primary"
            icon={<Plus size="16rpx" />}
            onClick={handleCreateAccount}
          >
            创建账号
          </Button>
        </View>

        {loading ? (
          <LoadingState text="加载中..." />
        ) : !isLoggedIn ? (
          <EmptyState
            title="请先登录"
            description="登录后查看您的账号信息"
            actionText="立即登录"
            onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
          />
        ) : userSummary?.gameAccounts && userSummary.gameAccounts.length > 0 ? (
          <View className="account-list">
            {userSummary.gameAccounts.map((account) => (
              <PageCard
                key={account.id}
                title={`${account.accountName}`}
                description={`${account.serverId}区\n加成：${
                  account.damageBonus
                }% · 战力${formatPower(account.powerValue)}`}
                icon={<User size="40rpx" color="#1976d2" />}
                extra={
                  <View className="account-extra">
                    <Text className="troop-level">
                      兵{account.troopLevel}级
                    </Text>
                  </View>
                }
                onClick={() => handleAccountDetail(account)}
              />
            ))}
          </View>
        ) : (
          <EmptyState
            title="暂无账号"
            style={emptyStyle}
            description="创建账号加入联盟参与联盟活动"
          />
        )}
      </View>

      {/* 演武场通知设置 */}
      {isLoggedIn && (
        <View className="section">
          <View className="section-header">
            <Text className="section-title">通知设置</Text>
          </View>
          <View className="notification-settings">
            <Cell
              title="演武场通知"
              titleStyle={{ flex: 5 }}
              brief="开启后将在每天 21:55 分接收参加演武场的提醒消息，确保订阅通知数量充足（可返回小程序首页查看）"
              icon={<Setting size="40rpx" color="#ff9800" />}
              rightIcon={
                <Switch
                  checked={arenaNotificationEnabled}
                  loading={arenaNotificationLoading}
                  onChange={handleArenaNotificationChange}
                />
              }
            />
          </View>
        </View>
      )}

      {/* 小助手说明 */}
      <View className="game-info">
        <Text className="info-title">小助手说明</Text>
        <View className="info-content">
          <Text className="info-text">• 每个区服最多可创建2个账号</Text>
          <Text className="info-text">• 每个账号只能加入一个联盟</Text>
          <Text className="info-text">• 联盟最多100名正式成员</Text>
          <Text className="info-text">
            • 支持官渡一、官渡二、攻城、守城四种战事
          </Text>
          <Text className="info-text">
            • 支持南蛮组队，支持发送、接收联盟通知
          </Text>
        </View>
      </View>
    </View>
  );
};

export default SanguoGame;
