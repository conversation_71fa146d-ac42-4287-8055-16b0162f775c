.sanguo-game-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;

  .quick-actions {
    margin-bottom: 48rpx;

    .action-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24rpx;

      .action-item {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 32rpx 16rpx;
        text-align: center;
        box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          opacity: 0.8;
        }

        .action-icon {
          width: 64rpx;
          height: 64rpx;
          background: rgba(25, 118, 210, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16rpx;
        }

        .action-text {
          font-size: 24rpx;
          color: #212121;
          font-weight: 500;
          display: block;
        }
      }
    }
  }

  .section {
    margin-bottom: 48rpx;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .section-title-wrapper {
        display: flex;
        align-items: center;
      }

      .section-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #212121;
      }

      .section-count {
        font-size: 28rpx;
        color: #616161;
        margin-left: 16rpx;
      }
    }

    .alliance-list,
    .account-list {
      .account-extra {
        display: flex;
        align-items: center;

        .troop-level {
          font-size: 24rpx;
          color: #1976d2;
          background: rgba(25, 118, 210, 0.1);
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          font-weight: 500;
        }
      }
    }
  }

  .game-info {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .info-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #212121;
      margin-bottom: 24rpx;
      display: block;
    }

    .info-content {
      .info-text {
        font-size: 28rpx;
        color: #616161;
        line-height: 1.6;
        margin-bottom: 16rpx;
        display: block;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 王朝状态样式
  .dynasty-status {
    display: flex;
    align-items: center;

    .status-badge {
      font-size: 24rpx;
      padding: 8rpx 12rpx;
      border-radius: 8rpx;
      font-weight: 500;

      &.enabled {
        color: #43a047;
        background: rgba(67, 160, 71, 0.1);
      }

      &.disabled {
        color: #616161;
        background: rgba(97, 97, 97, 0.1);
      }
    }
  }
}
