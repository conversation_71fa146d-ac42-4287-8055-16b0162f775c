import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import { useRouter, useDidShow } from "@tarojs/taro";
import {
  Button,
  Field,
  Form,
  Input,
  Dialog,
  Stepper,
  ActionSheet,
} from "@taroify/core";
import { User, Plus, Friends } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { barbarianGroupApi } from "@/services/api";
import { showError, showSuccess } from "@/utils";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import "./team.scss";

const BarbarianTeam: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn } = useAuth();
  const { accountId, allianceId } = router.params;

  const [loading, setLoading] = useState(true);
  const [currentGroup, setCurrentGroup] = useState<any>(null);
  const [allGroups, setAllGroups] = useState<any[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<any[]>([]);

  // 创建分组相关状态
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [createFormData, setCreateFormData] = useState({
    groupName: "",
    queueCount: 1,
    description: "",
  });

  // 查看分组详情相关状态
  const [showGroupDetail, setShowGroupDetail] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<any>(null);
  const [groupMembers, setGroupMembers] = useState<any[]>([]);

  // 队列数量筛选相关状态
  const [selectedQueueCount, setSelectedQueueCount] = useState<number | null>(
    null
  );
  const [showQueueActionSheet, setShowQueueActionSheet] = useState(false);

  const queueOptions = [
    { value: 1, label: "1队列" },
    { value: 2, label: "2队列" },
    { value: 3, label: "3队列" },
    { value: 4, label: "4队列" },
    { value: 5, label: "5队列" },
    { value: 6, label: "6队列" },
  ];

  useEffect(() => {
    if (isLoggedIn && accountId && allianceId) {
      loadData();
    }
  }, [isLoggedIn, accountId, allianceId]);

  useDidShow(() => {
    if (isLoggedIn && accountId && allianceId) {
      loadData();
    }
  });

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([loadCurrentGroup(), loadAllGroups()]);
    } catch (error) {
      console.error("加载数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 加载当前账号所在的分组
  const loadCurrentGroup = async () => {
    try {
      const response = await barbarianGroupApi.getAccountBarbarianGroup(
        accountId!
      );
      setCurrentGroup(response.data);
    } catch (error) {
      console.error("加载当前分组失败:", error);
      setCurrentGroup(null);
    }
  };

  // 加载联盟所有分组
  const loadAllGroups = async () => {
    try {
      const response = await barbarianGroupApi.getAllianceBarbarianGroups(
        allianceId!
      );
      setAllGroups(response.data);
      setFilteredGroups(response.data);
    } catch (error) {
      console.error("加载分组列表失败:", error);
      showError("加载分组列表失败");
    }
  };

  // 根据队列数量筛选分组
  const filterGroupsByQueue = async (queueCount: number | null) => {
    try {
      if (queueCount === null) {
        setFilteredGroups(allGroups);
      } else {
        const response = await barbarianGroupApi.getBarbarianGroupsByQueueCount(
          allianceId!,
          queueCount
        );
        setFilteredGroups(response.data);
      }
      setSelectedQueueCount(queueCount);
    } catch (error) {
      console.error("筛选分组失败:", error);
      showError("筛选分组失败");
    }
  };

  // 创建分组
  const handleCreateGroup = async () => {
    if (!createFormData.groupName.trim()) {
      showError("请输入分组名称");
      return;
    }

    try {
      // 使用创建并加入的接口
      await barbarianGroupApi.createAndJoinBarbarianGroup({
        allianceId: allianceId!,
        groupName: createFormData.groupName.trim(),
        queueCount: createFormData.queueCount,
        description: createFormData.description.trim(),
        accountId: accountId!,
      });

      showSuccess("分组创建成功并已加入");
      setShowCreateDialog(false);
      setCreateFormData({ groupName: "", queueCount: 1, description: "" });
      // 刷新页面数据
      loadData();
    } catch (error) {
      console.error("创建分组失败:", error);
      showError("创建分组失败");
    }
  };

  // 加入分组
  const handleJoinGroup = async (groupId: string) => {
    try {
      await barbarianGroupApi.joinBarbarianGroup({
        groupId,
        accountId: accountId!,
      });

      showSuccess("加入分组成功");
      // 刷新页面数据
      loadData();
    } catch (error) {
      console.error("加入分组失败:", error);
      showError("加入分组失败");
    }
  };

  // 离开分组
  const handleLeaveGroup = async () => {
    try {
      await barbarianGroupApi.leaveBarbarianGroup(accountId!);
      showSuccess("已离开分组");
      // 刷新页面数据
      loadData();
    } catch (error) {
      console.error("离开分组失败:", error);
      showError("离开分组失败");
    }
  };

  // 查看分组详情
  const handleViewGroupDetail = async (group: any) => {
    try {
      const response = await barbarianGroupApi.getBarbarianGroupDetail(
        group.id
      );
      setSelectedGroup(response.data.group);
      setGroupMembers(response.data.members || []);
      setShowGroupDetail(true);
    } catch (error) {
      console.error("加载分组详情失败:", error);
      showError("加载分组详情失败");
    }
  };

  if (loading) {
    return <LoadingState text="加载中..." />;
  }

  return (
    <View className="barbarian-team-page">
      {/* 提示信息 */}
      <View className="tip-section">
        <View className="tip-content">
          <Text className="tip-text">
            💡
            在游戏的右下方点击同盟→同盟成员→找到队友后点击队友，选择援助，可直接派兵援助，不需要队友发送城镇所在位置。
          </Text>
        </View>
      </View>

      {/* 当前分组信息 */}
      {currentGroup && (
        <View className="current-group-section">
          <View className="section-header">
            <Text className="section-title">我的分组</Text>
          </View>
          <View className="current-group-card">
            <PageCard
              title={currentGroup.groupName}
              description={`队列数量: ${currentGroup.queueCount} · 成员: ${
                currentGroup.memberCount || 0
              }人${
                currentGroup.description ? `\n${currentGroup.description}` : ""
              }`}
              icon={<Friends size="40rpx" color="#4caf50" />}
              onClick={() => handleViewGroupDetail(currentGroup)}
            />
            <View className="group-actions">
              <Button
                size="mini"
                color="danger"
                variant="outlined"
                onClick={handleLeaveGroup}
              >
                退出分组
              </Button>
            </View>
          </View>
        </View>
      )}

      {/* 标签页 */}
      <View className="groups-section">
        <View className="section-header">
          <Text className="section-title">联盟分组</Text>
          <View className="header-actions">
            <Button
              size="small"
              color="primary"
              variant="outlined"
              onClick={() => setShowQueueActionSheet(true)}
            >
              {selectedQueueCount ? `${selectedQueueCount}队列` : "全部队列"}
            </Button>
            <Button
              size="small"
              color="primary"
              icon={<Plus size="16rpx" />}
              disabled={!!currentGroup}
              onClick={() => setShowCreateDialog(true)}
            >
              创建分组
            </Button>
          </View>
        </View>

        {filteredGroups.length > 0 ? (
          <View className="groups-list">
            {filteredGroups.map((group) => (
              <PageCard
                key={group.id}
                title={group.groupName}
                description={`队列数量: ${group.queueCount} · 成员: ${
                  group.memberCount || 0
                }人${group.description ? `\n${group.description}` : ""}`}
                icon={<Friends size="40rpx" color="#1976d2" />}
                extra={
                  currentGroup?.id === group.id ? (
                    <Text className="joined-text">已加入</Text>
                  ) : (
                    <Button
                      size="mini"
                      color="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleJoinGroup(group.id);
                      }}
                    >
                      加入
                    </Button>
                  )
                }
                onClick={() => handleViewGroupDetail(group)}
              />
            ))}
          </View>
        ) : (
          <EmptyState
            title="暂无分组"
            description="联盟还没有南蛮分组"
            actionText="创建分组"
            onAction={() => setShowCreateDialog(true)}
          />
        )}
      </View>

      {/* 创建分组对话框 */}
      <Dialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        title="创建南蛮分组"
      >
        <Dialog.Content>
          <Form>
            <Field label="分组名称">
              <Input
                placeholder="请输入分组名称"
                value={createFormData.groupName}
                onChange={(e) =>
                  setCreateFormData((prev) => ({
                    ...prev,
                    groupName: e.detail.value,
                  }))
                }
              />
            </Field>
            <Field label="队列数量">
              <Stepper
                value={createFormData.queueCount}
                min={1}
                max={6}
                step={1}
                onChange={(value) => {
                  setCreateFormData((prev) => ({
                    ...prev,
                    queueCount: Number(value),
                  }));
                }}
              />
            </Field>
            <Field label="分组描述">
              <Input
                placeholder="请输入分组描述（可选）"
                value={createFormData.description}
                onChange={(e) =>
                  setCreateFormData((prev) => ({
                    ...prev,
                    description: e.detail.value,
                  }))
                }
              />
            </Field>
          </Form>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowCreateDialog(false)}>取消</Button>
          <Button color="primary" onClick={handleCreateGroup}>
            创建
          </Button>
        </Dialog.Actions>
      </Dialog>

      {/* 分组详情对话框 */}
      <Dialog
        open={showGroupDetail}
        onClose={() => setShowGroupDetail(false)}
        title="分组详情"
      >
        <Dialog.Content>
          {selectedGroup && (
            <View className="group-detail">
              <View className="detail-item">
                <Text className="detail-label">分组名称：</Text>
                <Text className="detail-value">{selectedGroup.groupName}</Text>
              </View>
              <View className="detail-item">
                <Text className="detail-label">队列数量：</Text>
                <Text className="detail-value">{selectedGroup.queueCount}</Text>
              </View>
              <View className="detail-item">
                <Text className="detail-label">成员数量：</Text>
                <Text className="detail-value">{groupMembers.length}人</Text>
              </View>
              {selectedGroup.description && (
                <View className="detail-item">
                  <Text className="detail-label">分组描述：</Text>
                  <Text className="detail-value">
                    {selectedGroup.description}
                  </Text>
                </View>
              )}

              <View className="members-section">
                <Text className="members-title">分组成员</Text>
                {groupMembers.length > 0 ? (
                  <View className="members-list">
                    {groupMembers.map((member, index) => (
                      <View key={index} className="member-item">
                        <User size="32rpx" color="#1976d2" />
                        <Text className="member-name">
                          {member.accountName || "未知账号"}
                        </Text>
                      </View>
                    ))}
                  </View>
                ) : (
                  <Text className="no-members">暂无成员</Text>
                )}
              </View>
            </View>
          )}
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowGroupDetail(false)}>关闭</Button>
        </Dialog.Actions>
      </Dialog>

      {/* 队列筛选 ActionSheet */}
      <ActionSheet
        open={showQueueActionSheet}
        onClose={() => setShowQueueActionSheet(false)}
        onCancel={() => setShowQueueActionSheet(false)}
      >
        <ActionSheet.Header>选择队列数量</ActionSheet.Header>
        <ActionSheet.Action
          onClick={() => {
            filterGroupsByQueue(null);
            setShowQueueActionSheet(false);
          }}
        >
          全部队列
        </ActionSheet.Action>
        {queueOptions.map((option) => (
          <ActionSheet.Action
            key={option.value}
            onClick={() => {
              filterGroupsByQueue(option.value);
              setShowQueueActionSheet(false);
            }}
          >
            {option.label}
          </ActionSheet.Action>
        ))}
      </ActionSheet>
    </View>
  );
};

export default BarbarianTeam;
