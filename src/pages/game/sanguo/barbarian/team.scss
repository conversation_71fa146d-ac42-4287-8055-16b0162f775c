.barbarian-team-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    padding: 0 16rpx;

    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #212121;
    }
  }

  .tip-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 32rpx;
    border-left: 8rpx solid #1976d2;

    .tip-content {
      .tip-text {
        font-size: 28rpx;
        color: #1565c0;
        line-height: 1.6;
      }
    }
  }

  .current-group-section,
  .groups-section {
    margin-bottom: 32rpx;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      padding: 0 16rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #212121;
      }

      .header-actions {
        display: flex;
        gap: 16rpx;
      }
    }

    .current-group-card {
      position: relative;

      .group-actions {
        position: absolute;
        top: 16rpx;
        right: 16rpx;
        z-index: 10;
      }
    }

    .groups-list {
      .joined-text {
        font-size: 24rpx;
        color: #4caf50;
        font-weight: 500;
      }
    }
  }

  // 对话框样式
  .group-detail {
    .detail-item {
      display: flex;
      margin-bottom: 16rpx;
      align-items: flex-start;

      .detail-label {
        font-size: 28rpx;
        color: #666;
        min-width: 140rpx;
        flex-shrink: 0;
      }

      .detail-value {
        font-size: 28rpx;
        color: #212121;
        flex: 1;
      }
    }

    .members-section {
      margin-top: 32rpx;
      padding-top: 24rpx;
      border-top: 1rpx solid #e0e0e0;

      .members-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
        display: block;
      }

      .members-list {
        .member-item {
          display: flex;
          align-items: center;
          gap: 16rpx;
          padding: 12rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .member-name {
            font-size: 28rpx;
            color: #212121;
          }
        }
      }

      .no-members {
        font-size: 26rpx;
        color: #999;
        text-align: center;
        padding: 32rpx 0;
      }
    }
  }

  .queue-options {
    .taroify-cell {
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .taroify-cell__title {
        font-size: 30rpx;
        color: #212121;
      }

      .taroify-cell__right-icon {
        color: #1976d2;
        font-weight: bold;
      }
    }
  }

  // 标签页样式
  .taroify-tabs {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .taroify-tabs__header {
      background: #fafafa;
      border-bottom: 1rpx solid #e0e0e0;

      .taroify-tab {
        font-size: 30rpx;
        font-weight: 500;

        &.taroify-tab--active {
          color: #1976d2;
          font-weight: 600;
        }
      }
    }

    .taroify-tabs__content {
      padding: 32rpx;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .barbarian-team-page {
    padding: 24rpx;

    .page-header {
      margin-bottom: 24rpx;

      .page-title {
        font-size: 32rpx;
      }
    }

    .tip-section {
      padding: 20rpx;
      margin-bottom: 24rpx;

      .tip-content {
        .tip-text {
          font-size: 26rpx;
        }
      }
    }

    .current-group-section,
    .groups-section {
      margin-bottom: 24rpx;

      .section-header {
        margin-bottom: 20rpx;

        .section-title {
          font-size: 30rpx;
        }

        .header-actions {
          gap: 12rpx;
        }
      }
    }

    .group-detail {
      .detail-item {
        margin-bottom: 12rpx;

        .detail-label {
          font-size: 26rpx;
          min-width: 120rpx;
        }

        .detail-value {
          font-size: 26rpx;
        }
      }

      .members-section {
        margin-top: 24rpx;
        padding-top: 20rpx;

        .members-title {
          font-size: 28rpx;
          margin-bottom: 12rpx;
        }

        .members-list {
          .member-item {
            padding: 10rpx 0;

            .member-name {
              font-size: 26rpx;
            }
          }
        }

        .no-members {
          font-size: 24rpx;
          padding: 24rpx 0;
        }
      }
    }

    .taroify-tabs {
      .taroify-tabs__header {
        .taroify-tab {
          font-size: 28rpx;
        }
      }

      .taroify-tabs__content {
        padding: 24rpx;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .barbarian-team-page {
    background-color: #121212;

    .page-header {
      .page-title {
        color: #ffffff;
      }
    }

    .tip-section {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
      border-left-color: #3b82f6;

      .tip-content {
        .tip-text {
          color: #dbeafe;
        }
      }
    }

    .current-group-section,
    .groups-section {
      .section-header {
        .section-title {
          color: #ffffff;
        }
      }
    }

    .taroify-tabs {
      background: #1e1e1e;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);

      .taroify-tabs__header {
        background: #2a2a2a;
        border-bottom-color: #333333;

        .taroify-tab {
          color: #cccccc;

          &.taroify-tab--active {
            color: #64b5f6;
          }
        }
      }
    }

    .group-detail {
      .detail-item {
        .detail-label {
          color: #888888;
        }

        .detail-value {
          color: #ffffff;
        }
      }

      .members-section {
        border-top-color: #333333;

        .members-title {
          color: #ffffff;
        }

        .members-list {
          .member-item {
            border-bottom-color: #333333;

            .member-name {
              color: #ffffff;
            }
          }
        }

        .no-members {
          color: #888888;
        }
      }
    }

    .queue-options {
      .taroify-cell {
        border-bottom-color: #333333;

        .taroify-cell__title {
          color: #ffffff;
        }

        .taroify-cell__right-icon {
          color: #64b5f6;
        }
      }
    }
  }
}
