import React, { useState, useEffect } from "react";
import { View, Text, Input } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Button, Cell } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { allianceApi } from "@/services/api";
import { AllianceForm } from "@/types";
import { showError, showSuccess } from "@/utils";
import "./form.scss";

const AllianceFormPage: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [allianceId, setAllianceId] = useState<string | null>(null);

  const [formData, setFormData] = useState<AllianceForm>({
    name: "",
    serverId: 0,
  });

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    const { id } = router.params;
    if (id) {
      setIsEdit(true);
      setAllianceId(id);
      loadAllianceData(id);
    }
  }, [isLoggedIn, authLoading]);

  const loadAllianceData = async (id: string) => {
    try {
      setLoading(true);
      const response = await allianceApi.getById(id);
      const alliance = response.data;
      setFormData({
        name: alliance.name,
        serverId: alliance.serverId,
      });
    } catch (error) {
      console.error("加载联盟数据失败:", error);
      showError("加载联盟数据失败");
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field: keyof AllianceForm, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      showError("请输入联盟名称");
      return false;
    }

    if (formData.name.length > 20) {
      showError("联盟名称不能超过20个字符");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      if (isEdit && allianceId) {
        await allianceApi.update(allianceId, formData);
        showSuccess("联盟更新成功");
      } else {
        await allianceApi.create(formData);
        showSuccess("联盟创建成功");
      }

      // 返回上一页
      Taro.navigateBack();
    } catch (error) {
      console.error("保存联盟失败:", error);
      showError(isEdit ? "更新联盟失败" : "创建联盟失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View className="alliance-form-page">
      <Cell.Group>
        {/* 区服 */}
        <Cell title="区服">
          <Input
            disabled={isEdit}
            value={formData.serverId === 0 ? "" : formData.serverId.toString()}
            placeholder="请输入区号"
            type="number"
            onInput={(e) => {
              const value = e.detail.value;
              const numValue = value === "" ? 0 : parseInt(value) || 0;
              handleFieldChange("serverId", numValue);
            }}
            style={{ textAlign: "right", flex: 1 }}
          />
        </Cell>

        {/* 联盟名称 */}
        <Cell title="联盟名称">
          <Input
            value={formData.name}
            placeholder="请输入联盟名称"
            maxlength={20}
            onInput={(e) => handleFieldChange("name", e.detail.value)}
            style={{ textAlign: "right", flex: 1 }}
          />
        </Cell>
      </Cell.Group>

      <View className="form-actions">
        <Button
          color="primary"
          size="large"
          loading={loading}
          onClick={handleSubmit}
        >
          {isEdit ? "更新联盟" : "创建联盟"}
        </Button>
      </View>

      <View className="form-tips">
        <Text className="tips-title">创建说明</Text>
        <View className="tips-content">
          <Text className="tips-text">• 联盟名称最多20个字符</Text>
          <Text className="tips-text">• 创建后将自动生成联盟编码</Text>
          <Text className="tips-text">• 您将成为联盟盟主，拥有管理权限</Text>
        </View>
      </View>
    </View>
  );
};

export default AllianceFormPage;
