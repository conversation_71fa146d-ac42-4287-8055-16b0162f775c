import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { But<PERSON>, Tabs } from "@taroify/core";
import { User, Checked, Close } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { allianceApplicationApi } from "@/services/api";
import { AllianceApplication } from "@/types";
import { showError, showSuccess } from "@/utils";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import "./applications.scss";

const AllianceApplications: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [allianceId, setAllianceId] = useState<string>("");
  const [pendingApplications, setPendingApplications] = useState<
    AllianceApplication[]
  >([]);
  const [processedApplications, setProcessedApplications] = useState<
    AllianceApplication[]
  >([]);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    const { id } = router.params;
    if (id) {
      setAllianceId(id);
      loadApplications(id);
    }
  }, [isLoggedIn, authLoading, router.params]);

  const loadApplications = async (id: string) => {
    try {
      setLoading(true);
      const response = await allianceApplicationApi.getByAlliance(id);
      console.log("联盟申请列表:", response.data);

      const pending = response.data.filter((app) => app.status === "PENDING");
      const processed = response.data.filter((app) => app.status !== "PENDING");

      setPendingApplications(pending);
      setProcessedApplications(processed);
    } catch (error) {
      console.error("加载申请列表失败:", error);
      showError("加载申请列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleApproveApplication = async (applicationId: string) => {
    try {
      await allianceApplicationApi.process(applicationId, true);
      showSuccess("申请已通过");
      loadApplications(allianceId);
    } catch (error) {
      console.error("审批申请失败:", error);
      showError("审批申请失败");
    }
  };

  const handleRejectApplication = async (applicationId: string) => {
    try {
      await allianceApplicationApi.process(applicationId, false);
      showSuccess("申请已拒绝");
      loadApplications(allianceId);
    } catch (error) {
      console.error("拒绝申请失败:", error);
      showError("拒绝申请失败");
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "待审核";
      case "approved":
        return "已通过";
      case "rejected":
        return "已拒绝";
      default:
        return "未知";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "var(--warning-color)";
      case "approved":
        return "var(--success-color)";
      case "rejected":
        return "var(--danger-color)";
      default:
        return "var(--text-color-secondary)";
    }
  };

  const renderApplicationCard = (
    application: AllianceApplication,
    showActions = false
  ) => (
    <PageCard
      key={application.id}
      title={application.account?.accountName || "-"}
      description={`${application.alliance?.serverId}区 · 战力${
        application.account?.powerValue || 0
      }`}
      icon={<User size="40rpx" color="var(--primary-color)" />}
      extra={
        <View className="application-extra">
          <View className="status-badge">
            <Text
              className="status-text"
              style={{ color: getStatusColor(application.status) }}
            >
              {getStatusText(application.status)}
            </Text>
          </View>
          {showActions && application.status === "PENDING" && (
            <View className="action-buttons">
              <Button
                size="mini"
                color="success"
                icon={<Checked />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleApproveApplication(application.id);
                }}
              >
                通过
              </Button>
              <Button
                size="mini"
                color="danger"
                variant="outlined"
                icon={<Close />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleRejectApplication(application.id);
                }}
              >
                拒绝
              </Button>
            </View>
          )}
        </View>
      }
    />
  );

  return (
    <View className="alliance-applications-page">
      <View className="page-header">
        <Text className="page-title">申请管理</Text>
        <Text className="page-subtitle">管理联盟加入申请</Text>
      </View>

      {loading ? (
        <LoadingState text="加载中..." />
      ) : (
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.TabPane title={`待审核 (${pendingApplications.length})`}>
            <View className="applications-list">
              {pendingApplications.length > 0 ? (
                pendingApplications.map((application) =>
                  renderApplicationCard(application, true)
                )
              ) : (
                <EmptyState
                  title="暂无待审核申请"
                  description="目前没有需要审核的申请"
                />
              )}
            </View>
          </Tabs.TabPane>

          <Tabs.TabPane title={`已处理 (${processedApplications.length})`}>
            <View className="applications-list">
              {processedApplications.length > 0 ? (
                processedApplications.map((application) =>
                  renderApplicationCard(application, false)
                )
              ) : (
                <EmptyState
                  title="暂无处理记录"
                  description="还没有处理过的申请"
                />
              )}
            </View>
          </Tabs.TabPane>
        </Tabs>
      )}
    </View>
  );
};

export default AllianceApplications;
