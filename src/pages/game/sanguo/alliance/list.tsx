import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { Button } from "@taroify/core";
import { Plus, ShieldOutlined } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { allianceApi } from "@/services/api";
import { Alliance } from "@/types";
import { showError } from "@/utils";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import "./list.scss";

const AllianceList: React.FC = () => {
  const { isLoggedIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [alliances, setAlliances] = useState<Alliance[]>([]);

  useEffect(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadAlliances();
    }
  }, [isLoggedIn]);

  // 页面显示时刷新数据
  useDidShow(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadAlliances();
    }
  });

  const loadAlliances = async () => {
    try {
      setLoading(true);
      const response = await allianceApi.getMyAlliances();
      console.log("我的联盟列表:", response.data);
      setAlliances(response.data);
    } catch (error) {
      console.error("加载联盟列表失败:", error);
      showError("加载联盟列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAlliance = () => {
    // 创建联盟需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/alliance/form",
    });
  };

  const handleAllianceDetail = (alliance: Alliance) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/alliance/detail?id=${alliance.id}`,
    });
  };

  const handleEditAlliance = (alliance: Alliance) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/alliance/form?id=${alliance.id}`,
    });
  };

  return (
    <View className="alliance-list-page">
      {/* 页面头部 */}
      <View className="page-header">
        <View className="header-content">
          <Text className="page-title">联盟管理</Text>
          <Text className="page-subtitle">管理您创建的联盟</Text>
        </View>
        <Button
          color="primary"
          size="small"
          icon={<Plus />}
          onClick={handleCreateAlliance}
        >
          创建联盟
        </Button>
      </View>

      {/* 联盟列表 */}
      <View className="alliance-list">
        {loading ? (
          <LoadingState text="加载中..." />
        ) : !isLoggedIn ? (
          <EmptyState
            title="请先登录"
            description="登录后查看您的联盟信息"
            actionText="立即登录"
            onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
          />
        ) : alliances.length > 0 ? (
          alliances.map((alliance) => (
            <PageCard
              key={alliance.id}
              title={alliance.name}
              description={`区服${alliance.serverId} · 成员${
                alliance.memberCount || 0
              }人 · 编码：${alliance.code}`}
              icon={<ShieldOutlined size="40rpx" color="#1976d2" />}
              extra={
                <View className="alliance-actions">
                  <Button
                    size="mini"
                    color="primary"
                    variant="outlined"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditAlliance(alliance);
                    }}
                  >
                    编辑
                  </Button>
                </View>
              }
              onClick={() => handleAllianceDetail(alliance)}
            />
          ))
        ) : (
          <EmptyState
            title="暂无联盟"
            description="您还没有创建任何联盟"
            actionText="创建联盟"
            onAction={handleCreateAlliance}
          />
        )}
      </View>

      {/* 操作说明 */}
      <View className="operation-tips">
        <Text className="tips-title">操作说明</Text>
        <View className="tips-content">
          <Text className="tips-text">• 点击联盟卡片查看详细信息</Text>
          <Text className="tips-text">
            • 点击&quot;编辑&quot;按钮修改联盟信息
          </Text>
          <Text className="tips-text">• 联盟创建后会自动生成唯一编码</Text>
          <Text className="tips-text">• 其他玩家可通过编码申请加入</Text>
        </View>
      </View>
    </View>
  );
};

export default AllianceList;
