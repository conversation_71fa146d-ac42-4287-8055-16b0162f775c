import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import {
  Button,
  Tabs,
  ActionSheet,
  Dialog,
  Field,
  Form,
  Input,
  Switch,
  Cell,
  Stepper,
} from "@taroify/core";
import {
  User,
  ShieldOutlined,
  Edit,
  Checked,
  Close,
  Share,
  Delete,
  Bell,
  Setting,
} from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import {
  allianceApi,
  allianceApplicationApi,
  accountApi,
  allianceMemberApi,
} from "@/services/api";
import { Alliance, GameAccount, AllianceApplication } from "@/types";
import { formatPower, showError, showSuccess } from "@/utils";
import PageCard from "@/components/PageCard";
import EmptyState from "@/components/EmptyState";
import LvbuStarLevel from "@/components/LvbuStarLevel";
import "./detail.scss";

const AllianceDetail: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [alliance, setAlliance] = useState<Alliance | null>(null);
  const [members, setMembers] = useState<GameAccount[]>([]);
  const [applications, setApplications] = useState<AllianceApplication[]>([]);
  const [activeTab, setActiveTab] = useState(0);

  // 审核设置相关状态
  const [approvalSettingsLoading, setApprovalSettingsLoading] = useState(false);

  // 官渡人数上限相关状态
  const [_, setWarLimitsLoading] = useState(false);
  const [warLimitStatus, setWarLimitStatus] = useState<any>(null);

  // 成员管理相关状态
  const [selectedMember, setSelectedMember] = useState<GameAccount | null>(
    null
  );
  const [showMemberActionSheet, setShowMemberActionSheet] = useState(false);
  const [showEditMemberDialog, setShowEditMemberDialog] = useState(false);
  const [showRemoveMemberDialog, setShowRemoveMemberDialog] = useState(false);
  const [showTransferDialog, setShowTransferDialog] = useState(false);
  const [editingMember, setEditingMember] = useState<GameAccount | null>(null);
  const [memberFormData, setMemberFormData] = useState({
    accountName: "",
    powerValue: "",
    damageBonus: 0,
    troopLevel: 1,
    rallyCapacity: 0,
    memberTier: "TIER_1",
  });
  // 删除联盟相关状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    const { id } = router.params;
    if (id) {
      loadAllianceDetail(id);
      loadAllianceMembers(id);
      loadPendingApplications(id);
      loadWarLimitStatus(id);
    }
  }, [isLoggedIn, authLoading]);

  // 页面显示时重新加载数据（从表单页面返回时会触发）
  useDidShow(() => {
    if (!authLoading && isLoggedIn) {
      const { id } = router.params;
      if (id) {
        loadAllianceDetail(id);
        loadAllianceMembers(id);
        loadPendingApplications(id);
        loadWarLimitStatus(id);
      }
    }
  });

  const loadAllianceDetail = async (id: string) => {
    try {
      setLoading(true);
      const response = await allianceApi.getById(id);
      console.log("联盟详情:", response.data);
      setAlliance(response.data);
    } catch (error) {
      console.error("加载联盟详情失败:", error);
      showError("加载联盟详情失败");
    } finally {
      setLoading(false);
    }
  };

  const loadAllianceMembers = async (id: string) => {
    try {
      const response = await allianceApi.getMembers(id);
      console.log("联盟成员:", response.data);
      setMembers(response.data);
    } catch (error) {
      console.error("加载联盟成员失败:", error);
      showError("加载联盟成员失败");
    }
  };

  const loadPendingApplications = async (id: string) => {
    try {
      const response = await allianceApplicationApi.getByAlliance(id);
      console.log("待处理申请:", response.data);
      setApplications(response.data);
    } catch (error) {
      console.error("加载申请列表失败:", error);
    }
  };

  // 加载官渡人数上限状态
  const loadWarLimitStatus = async (id: string) => {
    try {
      const response = await allianceApi.getWarLimitStatus(id);
      console.log("官渡人数上限状态:", response.data);
      setWarLimitStatus(response.data);
    } catch (error) {
      console.error("加载官渡人数上限状态失败:", error);
    }
  };

  const handleEditAlliance = () => {
    if (alliance) {
      Taro.navigateTo({
        url: `/pages/game/sanguo/alliance/form?id=${alliance.id}`,
      });
    }
  };

  const handleBattleManagement = () => {
    if (alliance) {
      Taro.navigateTo({
        url: `/pages/game/sanguo/battle/list?allianceId=${alliance.id}`,
      });
    }
  };

  const handleProcessApplication = async (
    applicationId: string,
    approved: boolean
  ) => {
    try {
      await allianceApplicationApi.process(applicationId, approved);
      showSuccess(approved ? "申请已通过" : "申请已拒绝");

      // 重新加载申请列表和成员列表
      if (alliance) {
        loadPendingApplications(alliance.id);
        if (approved) {
          loadAllianceMembers(alliance.id);
        }
      }
    } catch (error) {
      console.error("处理申请失败:", error);
      showError("处理申请失败");
    }
  };

  // 成员管理相关方法
  const handleMemberClick = (member: GameAccount) => {
    setSelectedMember(member);
    setShowMemberActionSheet(true);
  };

  const handleEditMember = () => {
    if (!selectedMember) return;

    setEditingMember(selectedMember);
    setMemberFormData({
      accountName: selectedMember.accountName || "",
      powerValue: selectedMember.powerValue || "",
      damageBonus: selectedMember.damageBonus || 0,
      troopLevel: selectedMember.troopLevel || 1,
      rallyCapacity: selectedMember.rallyCapacity || 0,
      memberTier: selectedMember.memberTier || "TIER_1",
    });
    setShowMemberActionSheet(false);
    setShowEditMemberDialog(true);
  };

  const handleRemoveMember = () => {
    setShowMemberActionSheet(false);
    setShowRemoveMemberDialog(true);
  };

  const handleSaveMemberEdit = async () => {
    if (!editingMember) return;

    try {
      await accountApi.updateWithTier(editingMember.id, memberFormData);
      showSuccess("更新成功");
      setShowEditMemberDialog(false);

      // 重新加载成员列表
      if (alliance) {
        loadAllianceMembers(alliance.id);
      }
    } catch (error) {
      console.error("更新失败:", error);
      showError("更新失败");
    }
  };

  const handleConfirmRemoveMember = async () => {
    if (!selectedMember) return;

    try {
      await allianceMemberApi.removeMember(selectedMember.id);
      showSuccess("成员移除成功");
      setShowRemoveMemberDialog(false);

      // 重新加载成员列表
      if (alliance) {
        loadAllianceMembers(alliance.id);
      }
    } catch (error) {
      console.error("移除成员失败:", error);
      showError("移除成员失败");
    }
  };

  const getMemberTierName = (tier: string) => {
    return tier ? `${tier.replace("TIER_", "")}阶` : "1阶";
  };

  // 更新联盟审核设置
  const handleUpdateApprovalSetting = async (
    settingType: "allianceJoinApprovalRequired" | "warJoinApprovalRequired",
    enabled: boolean
  ) => {
    if (!alliance) return;

    try {
      setApprovalSettingsLoading(true);
      const updateData = {
        allianceId: alliance.id,
        [settingType]: enabled,
      };

      const response = await allianceApi.updateApprovalSettings(updateData);
      setAlliance(response.data);

      const settingName =
        settingType === "allianceJoinApprovalRequired"
          ? "加入联盟审核"
          : "官渡申请审核";
      showSuccess(`${settingName}设置已${enabled ? "开启" : "关闭"}`);
    } catch (error) {
      console.error("更新审核设置失败:", error);
      showError("更新设置失败");
    } finally {
      setApprovalSettingsLoading(false);
    }
  };

  // 更新官渡人数上限
  const handleUpdateWarLimits = async (
    limitType: "guanduOneLimit" | "guanduTwoLimit",
    value: number
  ) => {
    if (!alliance) return;

    try {
      setWarLimitsLoading(true);
      const updateData = {
        allianceId: alliance.id,
        guanduOneLimit:
          limitType === "guanduOneLimit"
            ? value
            : alliance.guanduOneLimit || 40,
        guanduTwoLimit:
          limitType === "guanduTwoLimit"
            ? value
            : alliance.guanduTwoLimit || 40,
      };

      await allianceApi.updateWarLimits(updateData);

      // 更新本地状态
      setAlliance((prev) =>
        prev
          ? {
              ...prev,
              [limitType]: value,
            }
          : null
      );

      const limitName = limitType === "guanduOneLimit" ? "官渡一" : "官渡二";
      showSuccess(`${limitName}人数上限为${value}人`);

      // 重新加载状态
      if (alliance.id) {
        loadWarLimitStatus(alliance.id);
      }
    } catch (error) {
      console.error("更新官渡人数上限失败:", error);
      showError("更新人数上限失败");
    } finally {
      setWarLimitsLoading(false);
    }
  };

  // 邀请成员
  const handleInviteMember = () => {
    if (!alliance) return;

    // 显示分享菜单
    Taro.showShareMenu({
      withShareTicket: true,
    });

    showSuccess("请点击右上角分享按钮邀请成员");
  };

  // 复制联盟编码
  const handleCopyCode = () => {
    if (!alliance) return;

    Taro.setClipboardData({
      data: alliance.code,
      success: () => {
        showSuccess("联盟编码已复制到剪贴板");
      },
      fail: () => {
        showError("复制失败");
      },
    });
  };

  // 转交联盟
  const handleTransferAlliance = () => {
    if (!alliance) return;

    // 检查是否有其他成员可以转交
    const otherMembers = members.filter(
      (member) => member.id !== alliance.leaderId
    );
    if (otherMembers.length === 0) {
      showError("联盟中无其他成员，无法转交联盟");
      return;
    }

    setShowTransferDialog(true);
  };

  const handleConfirmTransfer = async (newLeader: GameAccount) => {
    if (!alliance) return;

    try {
      await allianceApi.transfer(alliance.id, newLeader.userId);
      showSuccess(`联盟已转交给 ${newLeader.accountName}`);
      setShowTransferDialog(false);

      // 重新加载联盟详情
      loadAllianceDetail(alliance.id);
    } catch (error) {
      console.error("转交联盟失败:", error);
      showError("转交联盟失败");
    }
  };

  // 删除联盟
  const handleDeleteAlliance = async () => {
    if (!alliance) return;

    try {
      await allianceApi.delete(alliance.id);
      showSuccess("联盟已删除");
      setShowDeleteDialog(false);
      Taro.navigateBack();
    } catch (error) {
      console.error("删除联盟失败:", error);
      showError("删除联盟失败");
    }
  };

  // 打开发送通知页面
  const handleOpenNotificationDialog = () => {
    if (!alliance) return;
    Taro.navigateTo({
      url: `/pages/game/sanguo/alliance/send-notification?allianceId=${
        alliance.id
      }&allianceName=${encodeURIComponent(alliance.name)}`,
    });
  };

  // 设置页面分享
  Taro.useShareAppMessage(() => {
    if (!alliance) return {};

    return {
      title: `邀请你加入联盟【${alliance.name}】`,
      path: `/pages/game/sanguo/alliance/invite?code=${alliance.code}&id=${
        alliance.id
      }&name=${encodeURIComponent(alliance.name)}&serverId=${
        alliance.serverId
      }`,
      imageUrl: "", // 可以设置分享图片
    };
  });

  if (!alliance && !loading) {
    return (
      <EmptyState
        title="联盟不存在"
        description="该联盟可能已被删除"
        actionText="返回列表"
        onAction={() => Taro.navigateBack()}
      />
    );
  }

  return (
    <View className="alliance-detail-page">
      {/* 联盟基本信息 */}
      <View className="alliance-info">
        <View className="alliance-header">
          <View className="alliance-icon">
            <ShieldOutlined size="60rpx" color="#1976d2" />
          </View>
          <View className="alliance-basic">
            <View className="alliance-name-row">
              <Text className="alliance-name">{alliance?.name}</Text>
              <Button
                size="mini"
                color="primary"
                variant="outlined"
                icon={<Edit />}
                onClick={handleEditAlliance}
              >
                编辑
              </Button>
            </View>
            <View className="alliance-info-row">
              <Text className="alliance-server">
                区服: {alliance?.serverId}区
              </Text>
              <View className="alliance-code-section">
                <Text className="alliance-code">编码: {alliance?.code}</Text>
                <Button
                  size="mini"
                  color="primary"
                  variant="text"
                  onClick={handleCopyCode}
                >
                  复制
                </Button>
              </View>
            </View>
          </View>
        </View>
        <View className="alliance-actions">
          <Button
            size="small"
            color="primary"
            variant="outlined"
            icon={<Bell />}
            onClick={handleOpenNotificationDialog}
          >
            发送活动通知
          </Button>
          <Button
            size="small"
            color="warning"
            variant="outlined"
            onClick={handleTransferAlliance}
          >
            转交联盟
          </Button>
          <Button
            size="small"
            color="success"
            variant="outlined"
            icon={<Share />}
            onClick={handleInviteMember}
          >
            邀请成员
          </Button>
        </View>
      </View>

      {/* 操作按钮 */}
      <View className="action-buttons">
        <Button color="primary" size="small" onClick={handleBattleManagement}>
          战事管理
        </Button>
      </View>

      {/* 标签页 */}
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.TabPane title={`成员列表 (${members.length})`}>
          <View className="members-list">
            {members.length > 0 ? (
              members.map((member) => (
                <PageCard
                  key={member.id}
                  title={`${getMemberTierName(member.memberTier)} ${
                    member.accountName
                  }`}
                  description={`加成: ${member.damageBonus || 0}% · 战力: ${
                    formatPower(member.powerValue) || "-"
                  }`}
                  icon={<User size="40rpx" color="var(--primary-color)" />}
                  extra={
                    <View className="member-extra">
                      <View className="member-lvbu">
                        <Text className="lvbu-text">吕布：</Text>
                        <LvbuStarLevel
                          level={member.lvbuStarLevel || 0}
                          size="small"
                        />
                      </View>
                      <View className="member-tier">
                        <Text className="tier-text">
                          兵等级: {member.troopLevel || "-"}
                        </Text>
                      </View>
                    </View>
                  }
                  onClick={() => handleMemberClick(member)}
                />
              ))
            ) : (
              <EmptyState title="暂无成员" description="联盟还没有成员加入" />
            )}
          </View>
        </Tabs.TabPane>

        <Tabs.TabPane title={`申请管理 (${applications.length})`}>
          <View className="applications-list">
            {applications.length > 0 ? (
              applications.map((application) => (
                <PageCard
                  key={application.id}
                  title={application.account?.accountName || "未知账号"}
                  description={`战力: ${
                    formatPower(application.account?.powerValue) || "-"
                  } · 加成：${
                    application.account?.damageBonus || 0
                  }% \n申请时间: ${application.createdAt}`}
                  icon={<User size="40rpx" color="var(--warning-color)" />}
                  extra={
                    <View className="application-actions">
                      <Button
                        size="mini"
                        color="success"
                        icon={<Checked size="24rpx" />}
                        onClick={() =>
                          handleProcessApplication(application.id, true)
                        }
                      >
                        通过
                      </Button>
                      <Button
                        size="mini"
                        color="danger"
                        variant="outlined"
                        icon={<Close size="24rpx" />}
                        onClick={() =>
                          handleProcessApplication(application.id, false)
                        }
                        style={{ marginLeft: "16rpx" }}
                      >
                        拒绝
                      </Button>
                    </View>
                  }
                />
              ))
            ) : (
              <EmptyState title="暂无申请" description="目前没有待处理申请" />
            )}
          </View>
        </Tabs.TabPane>

        <Tabs.TabPane title="联盟设置">
          <View className="alliance-settings">
            <View className="settings-section">
              <Text className="settings-title">审核设置</Text>
              <View className="settings-list">
                <Cell
                  title="加入联盟审核"
                  brief="开启后成员申请加入联盟需要管理员审核"
                  icon={<Setting size="40rpx" color="#1976d2" />}
                  rightIcon={
                    <Switch
                      checked={alliance?.allianceJoinApprovalRequired || false}
                      loading={approvalSettingsLoading}
                      onChange={(checked) =>
                        handleUpdateApprovalSetting(
                          "allianceJoinApprovalRequired",
                          checked
                        )
                      }
                    />
                  }
                />
                <Cell
                  title="官渡申请审核"
                  brief="开启后成员申请官渡需要管理员审核"
                  icon={<Setting size="40rpx" color="#ff9800" />}
                  rightIcon={
                    <Switch
                      checked={alliance?.warJoinApprovalRequired || false}
                      loading={approvalSettingsLoading}
                      onChange={(checked) =>
                        handleUpdateApprovalSetting(
                          "warJoinApprovalRequired",
                          checked
                        )
                      }
                    />
                  }
                />
              </View>
            </View>

            <View className="settings-section">
              <Text className="settings-title">官渡人数上限</Text>
              <View className="settings-list">
                <Cell
                  title="官渡一人数上限"
                  brief={
                    warLimitStatus
                      ? `当前已安排: ${warLimitStatus.guanduOneArranged || 0}人`
                      : "设置官渡一战事的人数上限"
                  }
                  icon={<Setting size="40rpx" color="#1976d2" />}
                  rightIcon={
                    <Stepper
                      value={alliance?.guanduOneLimit || 40}
                      min={1}
                      max={200}
                      step={1}
                      onChange={(value) =>
                        handleUpdateWarLimits("guanduOneLimit", Number(value))
                      }
                    />
                  }
                />
                <Cell
                  title="官渡二人数上限"
                  brief={
                    warLimitStatus
                      ? `当前已安排: ${warLimitStatus.guanduTwoArranged || 0}人`
                      : "设置官渡二战事的人数上限"
                  }
                  icon={<Setting size="40rpx" color="#ff9800" />}
                  rightIcon={
                    <Stepper
                      value={alliance?.guanduTwoLimit || 40}
                      min={1}
                      max={200}
                      step={1}
                      onChange={(value) =>
                        handleUpdateWarLimits("guanduTwoLimit", Number(value))
                      }
                    />
                  }
                />
              </View>
            </View>
          </View>
        </Tabs.TabPane>
      </Tabs>

      {/* 成员操作选择器 */}
      <ActionSheet
        open={showMemberActionSheet}
        onClose={() => setShowMemberActionSheet(false)}
      >
        <ActionSheet.Header>
          成员操作: {selectedMember?.accountName}
        </ActionSheet.Header>

        <ActionSheet.Action
          onClick={handleEditMember}
          style={{ color: "#667eea", fontWeight: "bold" }}
        >
          ✏️ 编辑成员信息
        </ActionSheet.Action>

        <ActionSheet.Action
          onClick={handleRemoveMember}
          style={{ color: "#ff4757", fontWeight: "bold" }}
        >
          🗑️ 移除成员
        </ActionSheet.Action>

        <ActionSheet.Action
          onClick={() => setShowMemberActionSheet(false)}
          style={{ color: "#999" }}
        >
          取消
        </ActionSheet.Action>
      </ActionSheet>

      {/* 编辑成员信息对话框 */}
      <Dialog
        open={showEditMemberDialog}
        onClose={() => setShowEditMemberDialog(false)}
        title="编辑成员信息"
      >
        <Dialog.Content>
          <Form>
            <Field label="账号名称">
              <Input
                value={memberFormData.accountName}
                onChange={(e) =>
                  setMemberFormData({
                    ...memberFormData,
                    accountName: e.detail.value,
                  })
                }
                placeholder="请输入账号名称"
              />
            </Field>
            <Field label="战力值">
              <Input
                value={memberFormData.powerValue}
                onChange={(e) =>
                  setMemberFormData({
                    ...memberFormData,
                    powerValue: e.detail.value,
                  })
                }
                placeholder="请输入战力值"
              />
            </Field>
            <Field label="伤害加成(%)">
              <Input
                type="number"
                value={memberFormData.damageBonus.toString()}
                onChange={(e) =>
                  setMemberFormData({
                    ...memberFormData,
                    damageBonus: parseFloat(e.detail.value) || 0,
                  })
                }
                placeholder="请输入伤害加成"
              />
            </Field>
            <Field label="兵等级">
              <Input
                type="number"
                value={memberFormData.troopLevel.toString()}
                onChange={(e) =>
                  setMemberFormData({
                    ...memberFormData,
                    troopLevel: parseInt(e.detail.value) || 1,
                  })
                }
                placeholder="请输入兵等级"
              />
            </Field>
            <Field label="集结容量(万)">
              <Input
                type="number"
                value={memberFormData.rallyCapacity.toString()}
                onChange={(e) =>
                  setMemberFormData({
                    ...memberFormData,
                    rallyCapacity: parseInt(e.detail.value) || 0,
                  })
                }
                placeholder="请输入集结容量"
              />
            </Field>
            <Field label="成员阶位">
              <View className="tier-selector">
                {["TIER_1", "TIER_2", "TIER_3", "TIER_4", "TIER_5"].map(
                  (tier) => (
                    <Button
                      key={tier}
                      size="mini"
                      color={
                        memberFormData.memberTier === tier
                          ? "primary"
                          : "default"
                      }
                      variant={
                        memberFormData.memberTier === tier
                          ? "contained"
                          : "outlined"
                      }
                      onClick={() =>
                        setMemberFormData({
                          ...memberFormData,
                          memberTier: tier,
                        })
                      }
                    >
                      {tier.replace("TIER_", "")} 阶
                    </Button>
                  )
                )}
              </View>
            </Field>
          </Form>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowEditMemberDialog(false)}>取消</Button>
          <Button color="primary" onClick={handleSaveMemberEdit}>
            保存
          </Button>
        </Dialog.Actions>
      </Dialog>

      {/* 移除成员确认对话框 */}
      <Dialog
        open={showRemoveMemberDialog}
        onClose={() => setShowRemoveMemberDialog(false)}
        title="确认移除成员"
      >
        <Dialog.Content>
          <Text>
            确定要移除成员"{selectedMember?.accountName}"吗？此操作不可撤销。
          </Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowRemoveMemberDialog(false)}>取消</Button>
          <Button color="danger" onClick={handleConfirmRemoveMember}>
            确认移除
          </Button>
        </Dialog.Actions>
      </Dialog>

      {/* 删除按钮区域 */}
      <View className="delete-section">
        <Button
          color="danger"
          variant="outlined"
          size="small"
          icon={<Delete />}
          onClick={() => setShowDeleteDialog(true)}
        >
          删除联盟
        </Button>
      </View>

      {/* 转交联盟对话框 */}
      <Dialog
        open={showTransferDialog}
        onClose={() => setShowTransferDialog(false)}
        title="转交联盟"
      >
        <Dialog.Content>
          <Text style={{ marginBottom: "16rpx" }}>选择新的联盟盟主：</Text>
          <View className="member-list">
            {members
              .filter((member) => member.id !== alliance?.leaderId)
              .map((member) => (
                <View
                  key={member.id}
                  className="member-item"
                  onClick={() => handleConfirmTransfer(member)}
                >
                  <View className="member-info">
                    <Text className="member-name">{member.accountName}</Text>
                    <Text className="member-power">
                      战力: {formatPower(member.powerValue)}
                    </Text>
                  </View>
                </View>
              ))}
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowTransferDialog(false)}>取消</Button>
        </Dialog.Actions>
      </Dialog>

      {/* 删除联盟确认对话框 */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        title="确认删除联盟"
      >
        <Dialog.Content>
          <Text>
            确定要删除联盟 "{alliance?.name}" 吗？
            {members.length > 0 || applications.length > 0 ? (
              <Text
                style={{
                  color: "#ff6b6b",
                  display: "block",
                  marginTop: "16rpx",
                }}
              >
                ⚠️
                删除联盟后，联盟中的成员将无家可归，所有申请也将被清除。此操作不可恢复！
              </Text>
            ) : (
              <Text style={{ display: "block", marginTop: "16rpx" }}>
                此操作不可恢复！
              </Text>
            )}
          </Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowDeleteDialog(false)}>取消</Button>
          <Button color="danger" onClick={handleDeleteAlliance}>
            确认删除
          </Button>
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

export default AllianceDetail;
