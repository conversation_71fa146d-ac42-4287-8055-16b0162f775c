import React, { useState, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Button, Form, Picker, DatetimePicker, Popup } from "@taroify/core";
import { ArrowRight } from "@taroify/icons";
import { allianceNotificationApi } from "@/services/api";
import { showError, showSuccess } from "@/utils";
import "./send-notification.scss";

const SendNotification: React.FC = () => {
  const router = useRouter();
  const { allianceId, allianceName } = router.params;

  const [formData, setFormData] = useState({
    activityType: "",
    startTime: "",
    remark: "",
  });
  const [sending, setSending] = useState(false);
  const [showActivityPicker, setShowActivityPicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // 活动类型选项
  const activityOptions = [
    { value: "SAN_YING_ZHAN_LV_BU", label: "三英战吕布" },
    { value: "GUAN_DU_BAO_MING", label: "官渡报名" },
    { value: "ZHU_JIU_LUN_YING_XIONG", label: "煮酒论英雄" },
    { value: "NAN_MAN_RU_QIN", label: "南蛮入侵" },
    { value: "GONG_CHENG", label: "攻城" },
    { value: "SHOU_CHENG", label: "守城" },
    { value: "SHUA_GONG_XUN", label: "刷功勋" },
  ];

  // 初始化默认时间为当前时间
  useEffect(() => {
    const now = new Date();
    const dateTimeString = `${now.getFullYear()}/${(now.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${now.getDate().toString().padStart(2, "0")} ${now
      .getHours()
      .toString()
      .padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;
    setFormData((prev) => ({
      ...prev,
      startTime: dateTimeString,
    }));
    setCurrentDateTime(now);
  }, []);

  // 获取活动类型显示名称
  const getActivityLabel = (value: string) => {
    const option = activityOptions.find((opt) => opt.value === value);
    return option ? option.label : "请选择活动类型";
  };

  // 处理活动类型选择
  const handleActivitySelect = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      activityType: value,
    }));
    setShowActivityPicker(false);
  };

  // 处理时间选择
  const handleTimeSelect = (value: Date) => {
    const dateTimeString = `${value.getFullYear()}/${(value.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${value.getDate().toString().padStart(2, "0")} ${value
      .getHours()
      .toString()
      .padStart(2, "0")}:${value.getMinutes().toString().padStart(2, "0")}`;
    setFormData((prev) => ({
      ...prev,
      startTime: dateTimeString,
    }));
    setCurrentDateTime(value);
    setShowTimePicker(false);
  };

  // 发送通知
  const handleSendNotification = async () => {
    if (!formData.activityType) {
      showError("请选择活动类型");
      return;
    }

    if (!formData.startTime) {
      showError("请选择开始时间");
      return;
    }

    try {
      setSending(true);
      await allianceNotificationApi.sendAllianceNotification({
        allianceId: allianceId!,
        activityType: formData.activityType,
        startTime: formData.startTime,
        remark: formData.remark,
      });
      showSuccess("通知已发送");
      // 返回联盟详情页
      Taro.navigateBack();
    } catch (error) {
      console.error("发送通知失败:", error);
      showError("发送通知失败");
    } finally {
      setSending(false);
    }
  };

  // 取消发送
  const handleCancel = () => {
    Taro.navigateBack();
  };

  return (
    <View className="send-notification-page">
      <View className="page-content">
        <View className="alliance-info">
          <Text className="alliance-name">
            {decodeURIComponent(allianceName || "联盟")}
          </Text>
          <Text className="page-description">向联盟成员发送活动预定通知</Text>
          <View>
            <Text style={{ fontSize: "24rpx", color: "#999" }}>
              请勿频繁向联盟成员发送相同的活动通知，如联盟成员订阅数量为 0
              将无法接收微信通知（小程序首页可查看订阅数量）
            </Text>
          </View>
        </View>

        <View className="form-container">
          <Form className="notification-form">
            <View className="form-field">
              <View className="field-row">
                <Text className="field-label">活动类型</Text>
                <View
                  className="picker-field"
                  onClick={() => setShowActivityPicker(true)}
                >
                  <Text
                    className={`picker-text ${
                      formData.activityType ? "" : "placeholder"
                    }`}
                  >
                    {getActivityLabel(formData.activityType)}
                  </Text>
                  <ArrowRight size="24rpx" color="#bdbdbd" />
                </View>
              </View>
            </View>

            <View className="form-field">
              <View className="field-row">
                <Text className="field-label">开始时间</Text>
                <View
                  className="picker-field"
                  onClick={() => setShowTimePicker(true)}
                >
                  <Text className="picker-text">
                    {formData.startTime || "请选择日期时间"}
                  </Text>
                  <ArrowRight size="24rpx" color="#bdbdbd" />
                </View>
              </View>
            </View>
          </Form>
        </View>

        <View className="action-buttons">
          <Button
            className="cancel-button"
            size="large"
            variant="outlined"
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button
            className="send-button"
            size="large"
            color="primary"
            loading={sending}
            onClick={handleSendNotification}
          >
            发送通知
          </Button>
        </View>
      </View>

      {/* 活动类型选择器 */}
      <Popup
        open={showActivityPicker}
        onClose={() => setShowActivityPicker(false)}
        placement="bottom"
        style={{ height: "50%" }}
      >
        <Picker
          onConfirm={(values) => handleActivitySelect(values[0] as string)}
          onCancel={() => setShowActivityPicker(false)}
        >
          <Picker.Toolbar>
            <Picker.Button>取消</Picker.Button>
            <Picker.Title>选择活动类型</Picker.Title>
            <Picker.Button>确认</Picker.Button>
          </Picker.Toolbar>
          <Picker.Column>
            {activityOptions.map((option) => (
              <Picker.Option key={option.value} value={option.value}>
                {option.label}
              </Picker.Option>
            ))}
          </Picker.Column>
        </Picker>
      </Popup>

      {/* 时间选择器 */}
      <Popup
        open={showTimePicker}
        onClose={() => setShowTimePicker(false)}
        placement="bottom"
        style={{ height: "50%" }}
      >
        <DatetimePicker
          type="datetime"
          value={currentDateTime}
          onConfirm={handleTimeSelect}
          onCancel={() => setShowTimePicker(false)}
          formatter={(type, val) => {
            if (type === "year") {
              return `${val}年`;
            }
            if (type === "month") {
              return `${val}月`;
            }
            if (type === "day") {
              return `${val}日`;
            }
            if (type === "hour") {
              return `${val}时`;
            }
            if (type === "minute") {
              return `${val}分`;
            }
            if (type === "second") {
              return `${val}秒`;
            }
            return val;
          }}
        >
          <DatetimePicker.Toolbar>
            <DatetimePicker.Button>取消</DatetimePicker.Button>
            <DatetimePicker.Title>选择日期时间</DatetimePicker.Title>
            <DatetimePicker.Button>确认</DatetimePicker.Button>
          </DatetimePicker.Toolbar>
        </DatetimePicker>
      </Popup>
    </View>
  );
};

export default SendNotification;
