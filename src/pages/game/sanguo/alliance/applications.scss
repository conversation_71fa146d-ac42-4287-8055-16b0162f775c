.alliance-applications-page {
  padding: 32rpx;
  background-color: var(--background-color);
  min-height: 100vh;

  .page-header {
    margin-bottom: 32rpx;
    padding: 32rpx;
    background-color: var(--white);
    border-radius: 16rpx;

    .page-title {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8rpx;
    }

    .page-subtitle {
      font-size: 28rpx;
      color: var(--text-color-secondary);
    }
  }

  .applications-list {
    .application-extra {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 12rpx;

      .status-badge {
        .status-text {
          font-size: 24rpx;
          font-weight: 500;
        }
      }

      .action-buttons {
        display: flex;
        gap: 8rpx;
      }
    }
  }
}
