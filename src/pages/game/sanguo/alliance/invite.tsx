import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Button, Dialog } from "@taroify/core";
import {
  User,
  Plus,
  ShieldOutlined,
  Star,
  LocationOutlined,
} from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import {
  accountApi,
  allianceMemberApi,
  allianceApplicationApi,
} from "@/services/api";
import { Alliance, GameAccount } from "@/types";
import { formatPower, showError, showSuccess } from "@/utils";
import { EmptyState, LoadingState } from "@/components";

import "./invite.scss";

const AllianceInvitePage: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [alliance, setAlliance] = useState<Alliance | null>(null);
  const [myAccounts, setMyAccounts] = useState<GameAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<GameAccount | null>(
    null
  );

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    const { code, id, name, serverId } = router.params;
    if (code && id && name && serverId) {
      // 从URL参数中构建联盟信息（未登录用户也可以查看）
      const allianceInfo = {
        id: id,
        code: code,
        name: decodeURIComponent(name),
        serverId: parseInt(serverId),
        leaderId: "", // 默认值，实际不影响邀请功能
        // 其他字段使用默认值
        memberCount: 0,
        maxMembers: 100,
        description: "",
        createdAt: "",
        updatedAt: "",
      };
      setAlliance(allianceInfo);

      // 只有登录用户才加载账号列表
      if (isLoggedIn) {
        loadMyAccounts();
      } else {
        // 未登录用户，设置loading为false
        setLoading(false);
      }
    } else {
      showError("邀请链接无效，缺少必要参数");
      Taro.navigateBack();
    }
  }, [isLoggedIn, authLoading]);

  const loadMyAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountApi.getMyAccounts();
      setMyAccounts(response.data);
    } catch (error) {
      console.error("加载账号列表失败:", error);
      showError("加载账号列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAccount = (account: GameAccount) => {
    setSelectedAccount(account);

    // 检查账号是否已在其他联盟
    if (account.alliance && account.alliance.code !== alliance?.code) {
      setShowConfirmDialog(true);
    } else if (account.alliance && account.alliance.code === alliance?.code) {
      showError("该账号已在此联盟中");
    } else {
      // 直接申请加入
      handleJoinAlliance(account);
    }
  };

  const handleJoinAlliance = async (account: GameAccount) => {
    if (!alliance) return;

    try {
      setSubmitting(true);
      await allianceApplicationApi.apply({
        accountId: account.id,
        allianceCode: alliance.code,
      });
      showSuccess(`已提交加入联盟申请，请等待审核`);
      setShowConfirmDialog(false);

      // 跳转到账号详情页面
      Taro.navigateTo({
        url: `/pages/game/sanguo/account/detail?id=${account.id}`,
      });
    } catch (error) {
      console.error("申请加入联盟失败:", error);
      showError("申请加入联盟失败");
    } finally {
      setSubmitting(false);
    }
  };

  const handleLeaveAndJoin = async () => {
    if (!selectedAccount || !alliance) return;

    try {
      setSubmitting(true);
      // 先退出当前联盟
      await allianceMemberApi.removeMember(selectedAccount.id);
      // 再申请加入新联盟
      await handleJoinAlliance(selectedAccount);
    } catch (error) {
      console.error("退出联盟失败:", error);
      showError("退出联盟失败");
      setSubmitting(false);
    }
  };

  const handleCreateAccount = () => {
    const { code, id, name, serverId } = router.params;
    const currentUrl = `/pages/game/sanguo/alliance/invite?code=${code}&id=${id}&name=${name}&serverId=${serverId}`;

    if (!isLoggedIn) {
      // 未登录用户，先跳转到登录页
      Taro.navigateTo({
        url: `/pages/login/index?redirect=${encodeURIComponent(currentUrl)}`,
      });
      return;
    }

    // 已登录用户，跳转到创建账号页面，创建完成后返回邀请页面
    Taro.navigateTo({
      url: `/pages/game/sanguo/account/form?redirect=${encodeURIComponent(
        currentUrl
      )}&serverId=${serverId}`,
    });
  };

  return (
    <View className="alliance-invite-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : !alliance ? (
        <EmptyState
          title="联盟不存在"
          description="该联盟可能已被删除"
          actionText="返回"
          onAction={() => Taro.navigateBack()}
        />
      ) : (
        <>
          {/* 联盟信息 */}
          <View className="alliance-info">
            <View className="alliance-icon">
              <ShieldOutlined size="80rpx" color="var(--primary-color)" />
            </View>

            <View className="alliance-header">
              <Text className="alliance-name">{alliance.name}</Text>
              <View className="alliance-details">
                <View className="detail-item">
                  <Star size="24rpx" color="#ffa726" />
                  <Text className="detail-text">编码: {alliance.code}</Text>
                </View>
                <View className="detail-item">
                  <LocationOutlined size="24rpx" color="#42a5f5" />
                  <Text className="detail-text">
                    区服: {alliance.serverId}区
                  </Text>
                </View>
              </View>
            </View>

            <View className="invite-section">
              <Text className="invite-text">
                邀请你加入联盟，一起征战三国！
              </Text>
            </View>
          </View>

          {/* 选择账号 */}
          <View className="account-selection">
            <View className="section-header">
              <Text className="section-title">选择要加入的账号</Text>
            </View>

            {!isLoggedIn ? (
              // 未登录用户显示登录提示
              <EmptyState
                title="请先登录"
                description="登录后查看您的账号并申请加入联盟"
                actionText="立即登录"
                onAction={() => {
                  // 保存当前页面路径，登录后返回
                  const { code, id, name, serverId } = router.params;
                  const currentUrl = `/pages/game/sanguo/alliance/invite?code=${code}&id=${id}&name=${name}&serverId=${serverId}`;
                  Taro.navigateTo({
                    url: `/pages/login/index?redirect=${encodeURIComponent(
                      currentUrl
                    )}`,
                  });
                }}
              />
            ) : myAccounts.length > 0 ? (
              <>
                <View className="account-list">
                  {myAccounts
                    .filter((account) => account.serverId === alliance.serverId)
                    .map((account) => (
                      <View
                        key={account.id}
                        className="account-item"
                        onClick={() => handleSelectAccount(account)}
                      >
                        <View className="account-icon">
                          <User size="40rpx" color="var(--primary-color)" />
                        </View>
                        <View className="account-info">
                          <Text className="account-name">
                            {account.accountName}
                          </Text>
                          <Text className="account-stats">
                            战力: {formatPower(account.powerValue)} · 加成:{" "}
                            {account.damageBonus}%
                          </Text>
                        </View>
                        <View className="account-status">
                          <Text
                            className={`status-text ${
                              account.alliance ? "joined" : "available"
                            }`}
                          >
                            {account.alliance
                              ? `已加入: ${account.alliance.name}`
                              : "未加入联盟"}
                          </Text>
                        </View>
                      </View>
                    ))}
                </View>

                {/* 创建新账号按钮区域 */}
                <View className="create-account-section">
                  <Button
                    color="success"
                    variant="outlined"
                    icon={<Plus size="20rpx" />}
                    onClick={handleCreateAccount}
                    block
                  >
                    创建新账号
                  </Button>
                </View>
              </>
            ) : (
              <EmptyState
                title="暂无游戏账号"
                description="请先创建游戏账号"
                actionText="创建账号"
                onAction={handleCreateAccount}
              />
            )}
          </View>

          {/* 退出联盟确认对话框 */}
          <Dialog
            open={showConfirmDialog}
            onClose={() => setShowConfirmDialog(false)}
            title="确认退出联盟"
          >
            <Dialog.Content>
              <Text>
                账号"{selectedAccount?.accountName}"已加入联盟"
                {selectedAccount?.alliance?.name}"， 是否要退出当前联盟并加入"
                {alliance.name}"？
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowConfirmDialog(false)}>取消</Button>
              <Button
                color="danger"
                loading={submitting}
                onClick={handleLeaveAndJoin}
              >
                确认退出并加入
              </Button>
            </Dialog.Actions>
          </Dialog>
        </>
      )}
    </View>
  );
};

export default AllianceInvitePage;
