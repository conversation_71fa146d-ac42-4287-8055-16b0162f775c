.send-notification-page {
  min-height: 100vh;
  background-color: #f8f9fa;

  .page-content {
    padding: 32rpx;
  }

  .alliance-info {
    padding: 48rpx 32rpx;
    text-align: center;
    border-radius: 16rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.2);

    .alliance-name {
      display: block;
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .page-description {
      font-size: 28rpx;
    }
  }

  .form-container {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
  }

  .notification-form {
    padding: 32rpx;

    .form-field {
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.remark-field {
        .field-label {
          margin-bottom: 12rpx;
        }
      }

      .field-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16rpx;
      }

      .field-label {
        font-size: 30rpx;
        flex-shrink: 0;
        min-width: 140rpx;
      }

      .picker-field {
        flex: 1;
        min-height: 72rpx;
        padding: 16rpx 24rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 2rpx solid #e3f2fd;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;

        &:active {
          background: #e3f2fd;
          border-color: #1976d2;
          transform: scale(0.98);
        }

        .picker-text {
          font-size: 28rpx;
          color: #212121;
          flex: 1;

          &.placeholder {
            color: #9e9e9e;
          }
        }
      }

      .remark-textarea {
        width: 100%;
        min-height: 200rpx;
        padding: 20rpx 24rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 2rpx solid #e3f2fd;
        font-size: 28rpx;
        color: #212121;
        line-height: 1.6;
        resize: none;
        box-sizing: border-box;
        transition: all 0.3s ease;
        margin-top: 12rpx;

        &::placeholder {
          color: #9e9e9e;
        }

        &:focus {
          border-color: #1976d2;
          background: white;
          box-shadow: 0 0 0 4rpx rgba(25, 118, 210, 0.1);
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 24rpx;
    margin-top: 64rpx;

    .cancel-button,
    .send-button {
      flex: 1;
      height: 96rpx;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .cancel-button {
      color: #616161;
      border-color: #e0e0e0;
      background: white;

      &:active {
        background: #f5f5f5;
        transform: scale(0.98);
      }
    }

    .send-button {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
      border: none;

      &:active {
        transform: translateY(2rpx) scale(0.98);
        box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
      }

      &.taroify-button--loading {
        background: #bdbdbd;
        box-shadow: none;
      }
    }
  }

  // 弹窗样式优化
  .taroify-popup {
    .taroify-picker,
    .taroify-datetime-picker {
      background: white;

      .taroify-picker__toolbar,
      .taroify-datetime-picker__toolbar {
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #e3f2fd;
        background: #fafafa;

        .taroify-button {
          font-size: 28rpx;
          font-weight: 500;
          padding: 16rpx 24rpx;

          &:first-child {
            color: #616161;
          }

          &:last-child {
            color: #1976d2;
          }
        }

        .taroify-picker__title,
        .taroify-datetime-picker__title {
          font-size: 32rpx;
          font-weight: 600;
          color: #212121;
        }
      }

      .taroify-picker-column {
        .taroify-picker-column__item {
          font-size: 32rpx;
          padding: 24rpx 0;
          color: #212121;

          &.taroify-picker-column__item--selected {
            color: #1976d2;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .send-notification-page {
    .page-content {
      padding: 24rpx;
    }

    .alliance-info {
      padding: 40rpx 24rpx;
      margin-bottom: 24rpx;

      .alliance-name {
        font-size: 36rpx;
      }

      .page-description {
        font-size: 26rpx;
      }
    }

    .form-container {
      margin-bottom: 24rpx;
    }

    .notification-form {
      padding: 24rpx;

      .form-field {
        margin-bottom: 20rpx;

        .field-label {
          font-size: 28rpx;
          min-width: 120rpx;
        }

        .picker-field {
          min-height: 64rpx;
          padding: 14rpx 20rpx;

          .picker-text {
            font-size: 26rpx;
          }
        }

        .remark-textarea {
          min-height: 160rpx;
          padding: 16rpx 20rpx;
          font-size: 26rpx;
        }
      }
    }

    .action-buttons {
      gap: 20rpx;
      margin-top: 48rpx;

      .cancel-button,
      .send-button {
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 30rpx;
      }
    }

    // 弹窗在小屏幕上的优化
    .taroify-popup {
      .taroify-picker,
      .taroify-datetime-picker {
        .taroify-picker__toolbar,
        .taroify-datetime-picker__toolbar {
          padding: 20rpx 24rpx;

          .taroify-button {
            font-size: 26rpx;
            padding: 12rpx 20rpx;
          }

          .taroify-picker__title,
          .taroify-datetime-picker__title {
            font-size: 30rpx;
          }
        }

        .taroify-picker-column {
          .taroify-picker-column__item {
            font-size: 30rpx;
            padding: 20rpx 0;
          }
        }
      }
    }
  }
}
