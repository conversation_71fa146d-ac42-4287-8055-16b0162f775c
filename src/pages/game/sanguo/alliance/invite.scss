.alliance-invite-page {
  min-height: 100vh;
  padding: 40rpx 32rpx;

  .alliance-info {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    text-align: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .alliance-icon {
      margin-bottom: 24rpx;
    }

    .alliance-header {
      margin-bottom: 24rpx;

      .alliance-name {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 16rpx;
      }

      .alliance-details {
        display: flex;
        justify-content: center;
        gap: 24rpx;
        flex-wrap: wrap;

        .detail-item {
          display: flex;
          align-items: center;
          gap: 8rpx;

          .detail-text {
            font-size: 28rpx;
            color: var(--text-secondary);
          }
        }
      }
    }

    .invite-section {
      .invite-text {
        font-size: 32rpx;
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }

  .account-selection {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .section-header {
      padding: 32rpx;
      border-bottom: 2rpx solid #e9ecef;
      background: #f8f9fa;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: var(--text-primary);
      }
    }

    .account-list {
      .account-item {
        display: flex;
        align-items: center;
        padding: 32rpx;
        border-bottom: 2rpx solid #e9ecef;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        margin: 0 16rpx;

        &:first-child {
          margin-top: 16rpx;
        }

        &:last-child {
          border-bottom: none;
          margin-bottom: 16rpx;
        }

        &:hover {
          background: #f8f9fa;
          border-radius: 8rpx;
          margin: 0 8rpx;

          &:first-child {
            margin-top: 8rpx;
          }

          &:last-child {
            margin-bottom: 8rpx;
          }
        }

        &:active {
          background: #e9ecef;
        }

        .account-icon {
          margin-right: 24rpx;
          flex-shrink: 0;
        }

        .account-info {
          flex: 1;
          min-width: 0;

          .account-name {
            display: block;
            font-size: 32rpx;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .account-stats {
            display: block;
            font-size: 26rpx;
            color: var(--text-secondary);
          }
        }

        .account-status {
          flex-shrink: 0;
          margin-left: 16rpx;

          .status-text {
            font-size: 24rpx;
            padding: 8rpx 16rpx;
            border-radius: 16rpx;

            &.available {
              color: #28a745;
              background: rgba(40, 167, 69, 0.1);
            }

            &.joined {
              color: #6c757d;
              background: rgba(108, 117, 125, 0.1);
            }
          }
        }
      }
    }

    .create-account-section {
      padding: 32rpx;
      border-top: 1rpx solid #f0f0f0;
      background: #fafafa;

      .taroify-button {
        height: 88rpx;
        font-size: 32rpx;
        font-weight: 500;
        border-radius: 16rpx;
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .alliance-invite-page {
    padding: 32rpx 24rpx;

    .alliance-info {
      padding: 40rpx 24rpx;

      .alliance-header {
        .alliance-name {
          font-size: 44rpx;
        }
      }

      .invite-text {
        font-size: 30rpx;
      }
    }

    .account-selection {
      .section-header {
        padding: 24rpx;

        .section-title {
          font-size: 30rpx;
        }
      }

      .account-list {
        .taroify-cell {
          padding: 20rpx 24rpx;

          .taroify-cell__title {
            font-size: 30rpx;
          }

          .taroify-cell__brief {
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
