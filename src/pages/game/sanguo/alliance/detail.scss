.alliance-detail-page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .alliance-info {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .alliance-header {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;

      .alliance-icon {
        margin-right: 24rpx;
      }

      .alliance-basic {
        flex: 1;

        .alliance-name-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16rpx;

          .alliance-name {
            font-size: 36rpx;
            font-weight: 600;
            color: #212121;
          }
        }

        .alliance-info-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 4rpx;

          .alliance-server {
            font-size: 28rpx;
            color: #616161;
          }

          .alliance-code-section {
            display: flex;
            align-items: center;
            gap: 12rpx;

            .alliance-code {
              font-size: 28rpx;
              color: #616161;
            }
          }
        }
      }
    }

    .alliance-description {
      margin-bottom: 24rpx;
      // padding: 24rpx;
      background-color: #f8f9fa;
      border-radius: 12rpx;

      .description-text {
        font-size: 28rpx;
        color: #616161;
        line-height: 1.6;
      }
    }

    .alliance-stats {
      display: flex;
      justify-content: space-around;

      .stat-item {
        text-align: center;

        .stat-value {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #1976d2;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 24rpx;
          color: #616161;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16rpx;
    margin-bottom: 32rpx;

    .taroify-button {
      flex: 1;
    }
  }

  .members-list {
    margin-top: 24rpx;

    .member-extra {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8rpx;

      .member-lvbu {
        display: flex;
        align-items: center;
        .lvbu-text {
          font-size: 24rpx;
          color: #666;
          font-weight: 500;
        }
      }

      .member-tier {
        .tier-text {
          font-size: 24rpx;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  .applications-list {
    margin-top: 24rpx;
    .application-actions {
      display: flex;
      align-items: center;
    }
  }

  .tier-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    padding: 8rpx 0;
  }

  .alliance-activities,
  .battle-records {
    padding: 32rpx 0;
  }

  // 联盟操作按钮布局
  .alliance-actions {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    flex-wrap: wrap;
    justify-content: space-between;

    .taroify-button {
      min-width: 120rpx;
      font-size: 24rpx;
    }
  }

  // 转交联盟对话框样式
  .member-list {
    max-height: 400rpx;
    overflow-y: auto;

    .member-item {
      padding: 16rpx;
      border: 1rpx solid var(--border-color);
      border-radius: 8rpx;
      margin-bottom: 12rpx;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: var(--background-light);
        border-color: var(--primary-color);
      }

      .member-info {
        .member-name {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 4rpx;
        }

        .member-power {
          font-size: 24rpx;
          color: var(--text-secondary);
        }
      }
    }
  }

  // 删除按钮区域
  .delete-section {
    margin-top: 48rpx;
    padding: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    display: flex;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
  }
}
