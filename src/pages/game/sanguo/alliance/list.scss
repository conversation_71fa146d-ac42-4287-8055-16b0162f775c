.alliance-list-page {
  padding: 32rpx;
  background-color: var(--background-color);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    padding: 32rpx;
    background-color: var(--white);
    border-radius: 16rpx;

    .header-content {
      flex: 1;

      .page-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 8rpx;
      }

      .page-subtitle {
        font-size: 28rpx;
        color: var(--text-color-secondary);
      }
    }
  }

  .alliance-list {
    .alliance-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }
  }

  .operation-tips {
    margin-top: 64rpx;
    padding: 32rpx;
    background-color: var(--white);
    border-radius: 16rpx;

    .tips-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 24rpx;
    }

    .tips-content {
      .tips-text {
        display: block;
        font-size: 28rpx;
        color: var(--text-color-secondary);
        line-height: 1.6;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
