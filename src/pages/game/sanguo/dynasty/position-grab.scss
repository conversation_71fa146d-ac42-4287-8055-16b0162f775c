.position-grab-page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .grab-container {
    .grab-header {
      text-align: center;
      margin-bottom: 32rpx;

      .grab-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 8rpx;
      }

      .grab-subtitle {
        display: block;
        font-size: 28rpx;
        color: #616161;
      }
    }

    .grab-info {
      background: #ffffff;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: 28rpx;
          color: #616161;
        }

        .info-value {
          font-size: 28rpx;
          color: #212121;
          font-weight: 500;
        }
      }
    }

    .position-content {
      padding: 24rpx 0;

      .time-slots-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 12rpx;

        .time-slot {
          aspect-ratio: 1;
          background: #ffffff;
          border-radius: 12rpx;
          padding: 16rpx 8rpx;
          text-align: center;
          border: 2rpx solid #e3f2fd;
          transition: all 0.2s ease;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          &.available {
            border-color: #1976d2;

            &:hover {
              background: rgba(25, 118, 210, 0.05);
              transform: translateY(-2rpx);
            }

            &:active {
              transform: scale(0.98);
            }
          }

          &.occupied {
            background: #f5f5f5;
            border-color: #bdbdbd;
            cursor: not-allowed;
          }

          &.my-slot {
            background: rgba(67, 160, 71, 0.1);
            border-color: #43a047;
          }

          &.disabled {
            background: #f5f5f5;
            border-color: #bdbdbd;
            opacity: 0.6;
            cursor: not-allowed;
          }

          .slot-time {
            display: block;
            font-size: 24rpx;
            font-weight: 600;
            color: #212121;
            margin-bottom: 4rpx;
          }

          .slot-status {
            display: block;
            font-size: 20rpx;
            color: #616161;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
          }
        }
      }
    }

    .selected-account {
      background: #ffffff;
      border-radius: 12rpx;
      padding: 24rpx;
      margin: 24rpx 0;
      display: flex;
      align-items: center;
      gap: 16rpx;
      box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.1);

      .account-label {
        font-size: 28rpx;
        color: #616161;
      }

      .account-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        flex: 1;
      }
    }

    .grab-warning {
      margin: 24rpx 0;
      padding: 24rpx;
      background: rgba(244, 67, 54, 0.1);
      border-radius: 12rpx;
      border-left: 4rpx solid #f44336;
      text-align: center;

      .warning-text {
        font-size: 28rpx;
        font-weight: 600;
        color: #f44336;
      }
    }

    .grab-tips {
      margin-top: 32rpx;
      padding: 24rpx;
      background: rgba(25, 118, 210, 0.05);
      border-radius: 12rpx;
      border-left: 4rpx solid #1976d2;

      .tip-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
      }

      .tip-text {
        display: block;
        font-size: 26rpx;
        color: #616161;
        line-height: 1.6;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 账号选择对话框样式
  .account-list {
    .account-item {
      padding: 24rpx;
      border: 1rpx solid #e3f2fd;
      border-radius: 8rpx;
      margin-bottom: 16rpx;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(25, 118, 210, 0.05);
        border-color: #1976d2;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .account-name {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 4rpx;
      }

      .account-server {
        font-size: 24rpx;
        color: #616161;
      }
    }
  }

  // 账号名称对话框样式
  .account-name-content {
    padding: 16rpx 0;

    .account-name-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .account-name-text {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      word-break: break-all;
      line-height: 1.4;
    }
  }
}
