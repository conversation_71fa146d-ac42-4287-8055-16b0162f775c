.position-settings-page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .settings-container {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .settings-header {
      text-align: center;
      margin-bottom: 48rpx;

      .settings-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 8rpx;
      }

      .settings-subtitle {
        display: block;
        font-size: 28rpx;
        color: #616161;
      }
    }

    .position-selector {
      margin-bottom: 32rpx;

      .selector-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
      }

      .position-tabs {
        display: flex;
        gap: 16rpx;

        .taroify-button {
          flex: 1;
        }
      }
    }

    // 添加时间设置样式
    .time-settings {
      margin-bottom: 32rpx;

      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 24rpx;
      }

      .time-setting-item {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        padding: 16rpx;
        border-radius: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .time-label {
          font-size: 28rpx;
          color: #616161;
          min-width: 200rpx;
          flex-shrink: 0;
        }

        .taroify-button {
          flex: 1;
          max-width: 300rpx;
        }
      }
    }

    .quick-settings {
      margin-bottom: 32rpx;

      .quick-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
      }

      .quick-buttons {
        display: flex;
        gap: 16rpx;

        .taroify-button {
          flex: 1;
        }
      }
    }

    .time-display {
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: rgba(25, 118, 210, 0.05);
      border-radius: 12rpx;

      .time-item {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .time-label {
          font-size: 28rpx;
          color: #616161;
          min-width: 160rpx;
        }

        .time-value {
          font-size: 28rpx;
          color: #212121;
          font-weight: 500;
        }
      }
    }

    .time-slots-section {
      margin: 32rpx 0;

      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 8rpx;
      }

      .section-desc {
        display: block;
        font-size: 24rpx;
        color: #616161;
        margin-bottom: 24rpx;
      }

      .time-slots-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16rpx;

        .time-slot-item {
          .taroify-checkbox {
            font-size: 24rpx;
          }
        }
      }
    }

    .settings-actions {
      margin-top: 48rpx;
    }

    .settings-tips {
      margin-top: 32rpx;
      padding: 24rpx;
      background: rgba(25, 118, 210, 0.05);
      border-radius: 12rpx;
      border-left: 4rpx solid #1976d2;

      .tip-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
      }

      .tip-text {
        display: block;
        font-size: 26rpx;
        color: #616161;
        line-height: 1.6;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 添加时间选择器弹窗样式
  .datetime-picker-popup {
    .taroify-popup {
      width: 100%;
      max-width: 600rpx;
    }

    .taroify-datetime-picker {
      width: 100%;

      .taroify-datetime-picker__content {
        width: 100%;
      }

      .taroify-picker-column {
        width: 100%;

        .taroify-picker-column__item {
          font-size: 32rpx;
          padding: 20rpx 0;
        }
      }
    }

    .taroify-datetime-picker__toolbar {
      padding: 24rpx 32rpx;
      border-bottom: 1rpx solid #ebedf0;

      .taroify-button {
        font-size: 28rpx;
        padding: 16rpx 24rpx;
      }

      .taroify-datetime-picker__title {
        font-size: 32rpx;
        font-weight: 600;
        color: #212121;
      }
    }
  }
}
