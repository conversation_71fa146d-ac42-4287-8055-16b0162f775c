import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Ta<PERSON>, <PERSON>ton, Dialog } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { positionApi, accountApi, dynastyApi } from "@/services/api";
import { GameAccount } from "@/types";
import { showError, showSuccess, getLocalDateString } from "@/utils";
import LoadingState from "@/components/LoadingState";

import "./position-grab.scss";

const PositionGrab: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [grabbing, setGrabbing] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<
    "TAI_WEI" | "SHANG_SHU_LING"
  >("TAI_WEI");
  const [currentAccount, setCurrentAccount] = useState<GameAccount | null>(
    null
  );
  const [dynastyDetail, setDynastyDetail] = useState<any>(null);
  const [grabResults, setGrabResults] = useState<any>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<number | null>(null);
  const [positionConfigs, setPositionConfigs] = useState<{
    [key: string]: any;
  }>({});
  const [showAccountNameDialog, setShowAccountNameDialog] = useState(false);
  const [selectedAccountName, setSelectedAccountName] = useState<string>("");

  const dynastyId = router.params.dynastyId;
  const accountId = router.params.accountId;

  // 检查是否可以在今天抢夺
  const checkCanGrabToday = (grabStartTime: string, grabEndTime: string) => {
    const today = getLocalDateString();
    const grabStartDate = getLocalDateString(new Date(grabStartTime));
    const grabEndDate = getLocalDateString(new Date(grabEndTime));
    return grabStartDate <= today && today <= grabEndDate;
  };

  // 获取当前官职的配置
  const getCurrentPositionConfig = () => {
    return positionConfigs[selectedPosition];
  };

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }
    if (!dynastyId) return;
    loadData();
  }, [dynastyId, isLoggedIn, authLoading]);

  // 当切换官职时重新加载抢夺结果
  useEffect(() => {
    const currentConfig = getCurrentPositionConfig();
    if (currentConfig && currentConfig.dutyDate) {
      loadGrabResults(currentConfig.dutyDate);
    }
  }, [selectedPosition, positionConfigs]);

  const loadData = async () => {
    try {
      setLoading(true);

      // 如果有accountId，加载指定账号信息
      if (accountId) {
        const accountResponse = await accountApi.getById(accountId);
        setCurrentAccount(accountResponse.data);
      }

      // 加载王朝详情，获取官职设置
      if (dynastyId) {
        const dynastyResponse = await dynastyApi.getById(dynastyId);
        setDynastyDetail(dynastyResponse.data);

        // 处理官职配置
        const dynastyDetail = dynastyResponse.data;
        if (
          dynastyDetail &&
          dynastyDetail.positions &&
          dynastyDetail.positions.length > 0
        ) {
          const configs: { [key: string]: any } = {};

          // 为每个官职创建配置
          dynastyDetail.positions.forEach((position: any) => {
            configs[position.positionType] = {
              ...position,
              canGrabToday: checkCanGrabToday(
                position.grabStartTime,
                position.grabEndTime
              ),
            };
          });

          setPositionConfigs(configs);

          // 加载当前选中官职的抢夺结果
          const currentConfig = configs[selectedPosition];
          if (currentConfig && currentConfig.dutyDate) {
            await loadGrabResults(currentConfig.dutyDate);
          }
        }
      }
    } catch (error) {
      console.error("加载数据失败:", error);
      showError("加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  const loadGrabResults = async (dutyDate?: string) => {
    if (!dynastyId) return;

    try {
      const targetDate = dutyDate || getLocalDateString();
      const response = await positionApi.getGrabResults(dynastyId, targetDate);
      setGrabResults(response.data);
    } catch (error) {
      console.error("加载抢夺结果失败:", error);
    }
  };

  const handleTimeSlotClick = (slot: number) => {
    if (!currentAccount) {
      showError("账号信息加载失败");
      return;
    }

    if (dynastyDetail && !dynastyDetail.dynasty?.grabEnabled) {
      showError("未开启官职抢夺，速速联系昏君！");
      return;
    }

    const currentConfig = getCurrentPositionConfig();
    if (!currentConfig || !currentConfig.canGrabToday) {
      showError("今日无法抢夺官职，速速联系昏君！");
      return;
    }

    setSelectedTimeSlot(slot);
    setShowConfirmDialog(true);
  };

  // 处理点击已占用时段，显示完整账号名称
  const handleOccupiedSlotClick = (accountName: string) => {
    setSelectedAccountName(accountName);
    setShowAccountNameDialog(true);
  };

  const handleConfirmGrab = async () => {
    if (!dynastyId || !currentAccount || selectedTimeSlot === null) return;

    try {
      setGrabbing(true);
      await positionApi.grab(dynastyId, currentAccount.id, {
        positionType: selectedPosition,
        timeSlot: selectedTimeSlot,
      });
      showSuccess("抢夺成功");
      setShowConfirmDialog(false);

      // 重新加载抢夺结果
      const currentConfig = getCurrentPositionConfig();
      if (currentConfig && currentConfig.dutyDate) {
        await loadGrabResults(currentConfig.dutyDate);
      }
    } catch (error) {
      console.error("抢夺失败:", error);
    } finally {
      setGrabbing(false);
    }
  };

  const renderTimeSlots = () => {
    const slots: React.ReactNode[] = [];
    const positionResults = grabResults?.grabResults?.[selectedPosition] || [];
    const availableSlots =
      grabResults?.availableTimeSlots?.[selectedPosition] || [];
    const currentConfig = getCurrentPositionConfig();

    // 解析禁用时段
    const disabledSlots: number[] = [];
    if (currentConfig?.disabledTimeSlots) {
      const disabledStr = currentConfig.disabledTimeSlots.toString();
      if (disabledStr.trim()) {
        disabledSlots.push(
          ...disabledStr
            .split(",")
            .map((s: string) => parseInt(s.trim()))
            .filter((n: number) => !isNaN(n))
        );
      }
    }

    for (let i = 0; i < 24; i++) {
      const timeLabel = `${i.toString().padStart(2, "0")}:00`;
      const slotResult = positionResults.find((r: any) => r.timeSlot === i);
      const isOccupied = !!slotResult;
      const isAvailable = availableSlots.includes(i);
      const isDisabledByConfig = disabledSlots.includes(i);
      const isMyAccount =
        slotResult &&
        currentAccount &&
        slotResult.accountId === currentAccount.id;

      let statusText = "禁用";
      let statusClass = "disabled";
      let fullAccountName = "";

      if (isOccupied) {
        fullAccountName = slotResult.account?.accountName || "已占用";
        statusText =
          fullAccountName.length > 4
            ? fullAccountName.substring(0, 4) + "..."
            : fullAccountName;
        statusClass = isMyAccount ? "my-slot" : "occupied";
      } else if (isDisabledByConfig) {
        statusText = "禁用";
        statusClass = "disabled";
      } else if (isAvailable) {
        statusText = "可抢夺";
        statusClass = "available";
      }

      const canClick =
        currentConfig?.canGrabToday &&
        isAvailable &&
        !isOccupied &&
        !isDisabledByConfig;
      const canShowFullName = isOccupied && fullAccountName.length > 4;

      slots.push(
        <View
          key={i}
          className={`time-slot ${statusClass}`}
          onClick={() => {
            if (canClick) {
              handleTimeSlotClick(i);
            } else if (canShowFullName) {
              handleOccupiedSlotClick(fullAccountName);
            }
          }}
        >
          <Text className="slot-time">{timeLabel}</Text>
          <Text className="slot-status">{statusText}</Text>
        </View>
      );
    }
    return slots;
  };

  return (
    <View className="position-grab-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : (
        <>
          <View className="grab-container">
            <View className="grab-header">
              <Text className="grab-title">官职抢夺</Text>
              <Text className="grab-subtitle">选择时段抢夺官职</Text>
            </View>

            {/* 抢夺信息 */}
            {getCurrentPositionConfig() ? (
              <View className="grab-info">
                <View className="info-row">
                  <Text className="info-label">官职：</Text>
                  <Text className="info-value">
                    {selectedPosition === "TAI_WEI" ? "太尉" : "尚书令"}
                  </Text>
                </View>
                <View className="info-row">
                  <Text className="info-label">任职日期：</Text>
                  <Text className="info-value">
                    {getCurrentPositionConfig().dutyDate || "未设置"}
                  </Text>
                </View>
                <View className="info-row">
                  <Text className="info-label">抢夺开始：</Text>
                  <Text className="info-value">
                    {getCurrentPositionConfig().grabStartTime || "未设置"}
                  </Text>
                </View>
                <View className="info-row">
                  <Text className="info-label">抢夺结束：</Text>
                  <Text className="info-value">
                    {getCurrentPositionConfig().grabEndTime || "未设置"}
                  </Text>
                </View>
                <View className="info-row">
                  <Text className="info-label">当前账号：</Text>
                  <Text className="info-value">
                    {currentAccount?.accountName || "未知"}
                  </Text>
                </View>
              </View>
            ) : (
              <View className="grab-info">
                <View className="info-row">
                  <Text className="info-label">状态：</Text>
                  <Text className="info-value">官职设置未配置</Text>
                </View>
              </View>
            )}

            {/* 抢夺状态提示 */}
            {dynastyDetail && !dynastyDetail.dynasty?.grabEnabled ? (
              <View className="grab-warning">
                <Text className="warning-text">
                  未开启官职抢夺，速速联系昏君！
                </Text>
              </View>
            ) : (
              getCurrentPositionConfig() &&
              !getCurrentPositionConfig().canGrabToday && (
                <View className="grab-warning">
                  <Text className="warning-text">
                    今日无法抢夺官职，速速联系昏君！
                  </Text>
                </View>
              )
            )}

            {/* 官职选择 */}
            <Tabs value={selectedPosition} onChange={setSelectedPosition}>
              <Tabs.TabPane value="TAI_WEI" title="太尉">
                <View className="position-content">
                  <View className="time-slots-grid">{renderTimeSlots()}</View>
                </View>
              </Tabs.TabPane>
              <Tabs.TabPane value="SHANG_SHU_LING" title="尚书令">
                <View className="position-content">
                  <View className="time-slots-grid">{renderTimeSlots()}</View>
                </View>
              </Tabs.TabPane>
            </Tabs>

            {/* 说明信息 */}
            <View className="grab-tips">
              <Text className="tip-title">抢夺说明：</Text>
              <Text className="tip-text">• 绿色时段表示你的账号已抢夺</Text>
              <Text className="tip-text">• 灰色时段表示已被其他成员抢夺</Text>
              <Text className="tip-text">• 蓝色时段表示可以抢夺</Text>
              <Text className="tip-text">• 点击可抢夺时段进行抢夺</Text>
            </View>
          </View>

          {/* 抢夺确认对话框 */}
          <Dialog
            open={showConfirmDialog}
            onClose={() => setShowConfirmDialog(false)}
          >
            <Dialog.Header>确认抢夺</Dialog.Header>
            <Dialog.Content>
              <Text>
                确认要抢夺 {selectedPosition === "TAI_WEI" ? "太尉" : "尚书令"}{" "}
                的 {selectedTimeSlot?.toString().padStart(2, "0")}:00-
                {selectedTimeSlot?.toString().padStart(2, "0")}:59 时段吗？
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowConfirmDialog(false)}>取消</Button>
              <Button
                color="primary"
                loading={grabbing}
                onClick={handleConfirmGrab}
              >
                确认抢夺
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 显示完整账号名称对话框 */}
          <Dialog
            open={showAccountNameDialog}
            onClose={() => setShowAccountNameDialog(false)}
          >
            <Dialog.Header>账号信息</Dialog.Header>
            <Dialog.Content>
              <View className="account-name-content">
                <Text className="account-name-label">账号名称：</Text>
                <Text className="account-name-text">{selectedAccountName}</Text>
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowAccountNameDialog(false)}>
                确定
              </Button>
            </Dialog.Actions>
          </Dialog>
        </>
      )}
    </View>
  );
};

export default PositionGrab;
