import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Button, Switch, Dialog, Input, Field } from "@taroify/core";
import { Star, SettingOutlined, DeleteOutlined, Edit } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { dynastyApi, positionApi } from "@/services/api";
import { Dynasty } from "@/types";
import { showError, showSuccess, getLocalDateString } from "@/utils";
import LoadingState from "@/components/LoadingState";

import "./detail.scss";

const DynastyDetail: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dynasty, setDynasty] = useState<Dynasty | null>(null);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [updateForm, setUpdateForm] = useState({
    name: "",
    serverId: 0,
  });

  const [grabResults, setGrabResults] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState<string>(
    getLocalDateString()
  );
  const [showAccountNameDialog, setShowAccountNameDialog] = useState(false);
  const [selectedAccountName, setSelectedAccountName] = useState<string>("");

  const dynastyId = router.params.id;

  useEffect(() => {
    console.log("王朝详情页 useEffect 触发:", {
      authLoading,
      isLoggedIn,
      dynastyId,
    });

    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      console.log("用户未登录，跳转到登录页");
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    if (!dynastyId) {
      console.log("缺少王朝ID参数");
      return;
    }

    console.log("开始加载王朝详情:", dynastyId);
    loadDynastyDetail();
  }, [dynastyId, isLoggedIn, authLoading]);

  const loadDynastyDetail = async () => {
    if (!dynastyId) {
      console.log("loadDynastyDetail: 缺少王朝ID");
      return;
    }

    try {
      console.log("loadDynastyDetail: 开始加载王朝详情", dynastyId);
      setLoading(true);

      const response = await dynastyApi.getById(dynastyId);
      console.log("loadDynastyDetail: 王朝详情响应:", response.data);

      setDynasty(response.data.dynasty);

      // 初始化更新表单
      if (response.data.dynasty) {
        setUpdateForm({
          name: response.data.dynasty.name,
          serverId: response.data.dynasty.serverId,
        });
      }

      // 加载抢夺结果
      await loadGrabResults();
      console.log("loadDynastyDetail: 加载完成");
    } catch (error) {
      console.error("loadDynastyDetail: 加载王朝详情失败:", error);
      showError("加载王朝详情失败");
    } finally {
      setLoading(false);
    }
  };

  const loadGrabResults = async (date?: string) => {
    if (!dynastyId) return;

    try {
      const targetDate = date || selectedDate;
      const response = await positionApi.getGrabResults(dynastyId, targetDate);
      setGrabResults(response.data);
    } catch (error) {
      console.error("加载抢夺结果失败:", error);
    }
  };

  const handleToggleGrab = async (enabled: boolean) => {
    if (!dynasty) return;

    try {
      await dynastyApi.toggleGrab(dynasty.id, enabled);
      setDynasty({ ...dynasty, grabEnabled: enabled });
      showSuccess(enabled ? "官职抢夺已开启" : "官职抢夺已关闭");
    } catch (error) {
      console.error("切换抢夺状态失败:", error);
      showError("操作失败");
    }
  };

  const handlePositionSettings = () => {
    if (!dynasty) return;
    Taro.navigateTo({
      url: `/pages/game/sanguo/dynasty/position-settings?dynastyId=${dynasty.id}`,
    });
  };

  const handleClearResults = async () => {
    if (!dynasty) return;

    try {
      await dynastyApi.clearAllGrabResults(dynasty.id);
      showSuccess("抢夺结果已清空");
      setShowClearDialog(false);
      loadDynastyDetail();
    } catch (error) {
      console.error("清空抢夺结果失败:", error);
      showError("操作失败");
    }
  };

  const handleUpdateDynasty = async () => {
    if (!dynasty || !updateForm.name || updateForm.serverId <= 0) {
      showError("请填写完整信息");
      return;
    }

    try {
      console.log("开始更新王朝:", dynasty.id, updateForm);

      const response = await dynastyApi.update(dynasty.id, updateForm);
      console.log("更新王朝成功:", response);

      setShowUpdateDialog(false);

      // 直接更新本地状态，避免重新加载导致的闪烁
      setDynasty((prev) =>
        prev
          ? {
              ...prev,
              name: updateForm.name,
              serverId: updateForm.serverId,
            }
          : null
      );
    } catch (error) {
      console.error("更新王朝失败:", error);
      showError("更新失败");
    }
  };

  const handleCopyCode = () => {
    if (!dynasty) return;

    Taro.setClipboardData({
      data: dynasty.code,
      success: () => {
        showSuccess("王朝编码已复制");
      },
      fail: () => {
        showError("复制失败");
      },
    });
  };

  // 处理点击已占用时段，显示完整账号名称
  const handleOccupiedSlotClick = (accountName: string) => {
    setSelectedAccountName(accountName);
    setShowAccountNameDialog(true);
  };

  const renderTimeSlots = (positionType: "TAI_WEI" | "SHANG_SHU_LING") => {
    const slots: React.ReactNode[] = [];
    const positionResults = grabResults?.grabResults?.[positionType] || [];

    for (let i = 0; i < 24; i++) {
      const timeLabel = `${i.toString().padStart(2, "0")}:00`;
      const slotResult = positionResults.find((r: any) => r.timeSlot === i);
      const isOccupied = !!slotResult;
      const isDisabled = false; // TODO: 从设置中获取禁用状态

      let statusText =
        selectedDate === getLocalDateString() ? "无人抢夺" : "待抢夺";
      let statusClass = "available";

      let fullAccountName = "";

      if (isDisabled) {
        statusText = "禁用";
        statusClass = "disabled";
      } else if (isOccupied) {
        fullAccountName = slotResult.account?.accountName || "已占用";
        statusText =
          fullAccountName.length > 4
            ? fullAccountName.substring(0, 4) + "..."
            : fullAccountName;
        statusClass = "occupied";
      }

      const canShowFullName = isOccupied && fullAccountName.length > 4;

      slots.push(
        <View
          key={i}
          className={`time-slot ${statusClass}`}
          onClick={() =>
            canShowFullName && handleOccupiedSlotClick(fullAccountName)
          }
        >
          <Text className="slot-time">{timeLabel}</Text>
          <Text className="slot-status">{statusText}</Text>
        </View>
      );
    }
    return slots;
  };

  const handleDeleteDynasty = async () => {
    if (!dynasty) return;

    try {
      await dynastyApi.delete(dynasty.id);
      showSuccess("王朝已删除");
      Taro.navigateBack();
    } catch (error) {
      console.error("删除王朝失败:", error);
      showError("删除失败");
    }
  };

  if (!dynasty && !loading) {
    return (
      <View className="dynasty-detail-page">
        <View className="error-state">
          <Text>王朝不存在</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="dynasty-detail-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : dynasty ? (
        <>
          {/* 王朝基本信息 */}
          <View className="dynasty-info">
            <View className="dynasty-header">
              <View className="dynasty-icon">
                <Star size="48rpx" color="#1976d2" />
              </View>
              <View className="dynasty-basic">
                <Text className="dynasty-name">{dynasty.name}</Text>
                <View className="dynasty-code-row">
                  <Text className="dynasty-code">编码: {dynasty.code}</Text>
                  <Button
                    size="mini"
                    color="primary"
                    variant="text"
                    onClick={handleCopyCode}
                  >
                    复制
                  </Button>
                </View>
                <Text className="dynasty-server">{dynasty.serverId}区</Text>
              </View>
              <View className="dynasty-actions">
                <Button
                  size="small"
                  color="primary"
                  variant="outlined"
                  icon={<Edit size="16rpx" />}
                  onClick={() => setShowUpdateDialog(true)}
                >
                  更新
                </Button>
                <Button
                  size="small"
                  color="danger"
                  variant="outlined"
                  icon={<DeleteOutlined size="16rpx" />}
                  onClick={() => setShowDeleteDialog(true)}
                >
                  删除
                </Button>
              </View>
            </View>
          </View>

          {/* 官职抢夺控制 */}
          <View className="grab-control">
            <View className="control-header">
              <Text className="control-title">官职抢夺</Text>
              <Switch
                size={"24"}
                checked={dynasty.grabEnabled}
                onChange={handleToggleGrab}
              />
            </View>
            <Text className="control-desc">
              {dynasty.grabEnabled ? "成员可以抢夺官职" : "官职抢夺已关闭"}
            </Text>
          </View>

          {/* 操作按钮 */}
          <View className="dynasty-actions">
            <Button
              color="primary"
              icon={<SettingOutlined size="20rpx" />}
              onClick={handlePositionSettings}
            >
              官职设置
            </Button>
            <Button
              color="warning"
              variant="outlined"
              onClick={() => setShowClearDialog(true)}
            >
              清空结果
            </Button>
          </View>

          {/* 官职抢夺结果 */}
          <View className="grab-results">
            <View className="results-header">
              <Text className="results-title">官职抢夺结果</Text>
              <View className="date-selector">
                <Button
                  size="small"
                  color={
                    selectedDate === getLocalDateString()
                      ? "primary"
                      : "default"
                  }
                  onClick={() => {
                    const today = getLocalDateString();
                    setSelectedDate(today);
                    loadGrabResults(today);
                  }}
                >
                  今日
                </Button>
                <Button
                  size="small"
                  color={
                    selectedDate ===
                    getLocalDateString(
                      new Date(Date.now() + 24 * 60 * 60 * 1000)
                    )
                      ? "primary"
                      : "default"
                  }
                  onClick={() => {
                    const tomorrow = getLocalDateString(
                      new Date(Date.now() + 24 * 60 * 60 * 1000)
                    );
                    setSelectedDate(tomorrow);
                    loadGrabResults(tomorrow);
                  }}
                >
                  明日
                </Button>
              </View>
            </View>

            {/* 太尉抢夺结果 */}
            <View className="position-results">
              <Text className="position-title">太尉</Text>
              <View className="time-slots-grid">
                {renderTimeSlots("TAI_WEI")}
              </View>
            </View>

            {/* 尚书令抢夺结果 */}
            <View className="position-results">
              <Text className="position-title">尚书令</Text>
              <View className="time-slots-grid">
                {renderTimeSlots("SHANG_SHU_LING")}
              </View>
            </View>
          </View>

          {/* 清空确认对话框 */}
          <Dialog
            open={showClearDialog}
            onClose={() => setShowClearDialog(false)}
          >
            <Dialog.Header>确认清空</Dialog.Header>
            <Dialog.Content>
              <Text>确定要清空所有抢夺结果吗？此操作不可恢复。</Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowClearDialog(false)}>取消</Button>
              <Button color="danger" onClick={handleClearResults}>
                确认清空
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 更新王朝对话框 */}
          <Dialog
            open={showUpdateDialog}
            onClose={() => setShowUpdateDialog(false)}
          >
            <Dialog.Header>更新王朝</Dialog.Header>
            <Dialog.Content>
              <View className="update-form">
                <Field label="王朝名称">
                  <Input
                    value={updateForm.name}
                    onChange={(e) =>
                      setUpdateForm({ ...updateForm, name: e.detail.value })
                    }
                    placeholder="请输入王朝名称"
                  />
                </Field>
                <Field label="区号">
                  <Input
                    disabled={true}
                    type="number"
                    value={
                      updateForm.serverId > 0
                        ? updateForm.serverId.toString()
                        : ""
                    }
                  />
                </Field>
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowUpdateDialog(false)}>取消</Button>
              <Button color="primary" onClick={handleUpdateDynasty}>
                确认更新
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 删除王朝确认对话框 */}
          <Dialog
            open={showDeleteDialog}
            onClose={() => setShowDeleteDialog(false)}
          >
            <Dialog.Header>确认删除</Dialog.Header>
            <Dialog.Content>
              <Text>
                确定要删除王朝 "{dynasty.name}"
                吗？此操作不可恢复，所有相关数据将被永久删除。
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowDeleteDialog(false)}>取消</Button>
              <Button color="danger" onClick={handleDeleteDynasty}>
                确认删除
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 显示完整账号名称对话框 */}
          <Dialog
            open={showAccountNameDialog}
            onClose={() => setShowAccountNameDialog(false)}
          >
            <Dialog.Header>账号信息</Dialog.Header>
            <Dialog.Content>
              <View className="account-name-content">
                <Text className="account-name-label">账号名称：</Text>
                <Text className="account-name-text">{selectedAccountName}</Text>
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowAccountNameDialog(false)}>
                确定
              </Button>
            </Dialog.Actions>
          </Dialog>
        </>
      ) : null}
    </View>
  );
};

export default DynastyDetail;
