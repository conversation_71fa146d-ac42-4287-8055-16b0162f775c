import React, { useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { Button, Field, Form, Input } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { dynastyApi } from "@/services/api";
import { showError, showSuccess, showLoading, hideLoading } from "@/utils";
import "./form.scss";

interface DynastyFormData {
  name: string;
  serverId: number;
}

const DynastyForm: React.FC = () => {
  const { isLoggedIn } = useAuth();
  const [formData, setFormData] = useState<DynastyFormData>({
    name: "",
    serverId: 0,
  });

  const handleSubmit = async () => {
    if (!isLoggedIn) {
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    if (!formData.name.trim()) {
      showError("请输入王朝名称");
      return;
    }

    if (!formData.serverId || formData.serverId < 1) {
      showError("请输入正确的区号");
      return;
    }

    try {
      showLoading("创建中...");

      await dynastyApi.create({
        name: formData.name.trim(),
        serverId: formData.serverId,
      });

      showSuccess("王朝创建成功");
      Taro.navigateBack();
    } catch (error) {
      console.error("创建王朝失败:", error);
      showError("创建王朝失败");
    } finally {
      hideLoading();
    }
  };

  return (
    <View className="dynasty-form-page">
      <View className="form-container">
        <View className="form-header">
          <Text className="form-title">创建王朝</Text>
          <Text className="form-subtitle">每个区只能创建一个王朝</Text>
        </View>

        <Form>
          <Field label="王朝名称" required>
            <Input
              placeholder="请输入王朝名称"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.detail.value })
              }
            />
          </Field>

          <Field label="区号" required>
            <Input
              type="number"
              placeholder="请输入区号"
              value={formData.serverId > 0 ? formData.serverId.toString() : ""}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  serverId: parseInt(e.detail.value) || 0,
                })
              }
            />
          </Field>
        </Form>

        <View className="form-actions">
          <Button color="primary" block onClick={handleSubmit}>
            创建王朝
          </Button>
        </View>

        <View className="form-tips">
          <Text className="tip-title">创建说明：</Text>
          <Text className="tip-text">• 每个区只能创建一个王朝</Text>
          <Text className="tip-text">• 创建后你将成为该王朝的天子</Text>
          <Text className="tip-text">• 天子可以管理官职抢夺设置</Text>
          <Text className="tip-text">• 王朝成员可以抢夺太尉和尚书令官职</Text>
        </View>
      </View>
    </View>
  );
};

export default DynastyForm;
