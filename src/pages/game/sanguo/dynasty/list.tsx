import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { Button } from "@taroify/core";
import { Plus, Star } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { dynastyApi } from "@/services/api";
import { Dynasty } from "@/types";
import { showError, formatDate } from "@/utils";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import "./list.scss";

const DynastyList: React.FC = () => {
  const { isLoggedIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [dynasties, setDynasties] = useState<Dynasty[]>([]);

  useEffect(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadDynasties();
    }
  }, [isLoggedIn]);

  // 页面显示时刷新数据
  useDidShow(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadDynasties();
    }
  });

  const loadDynasties = async () => {
    try {
      setLoading(true);
      const response = await dynastyApi.getMy();
      console.log("王朝列表响应:", response.data);
      setDynasties(response.data || []);
    } catch (error) {
      console.error("加载王朝列表失败:", error);
      showError("加载王朝列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDynasty = () => {
    // 创建王朝需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/dynasty/form",
    });
  };

  const handleDynastyDetail = (dynasty: Dynasty) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/dynasty/detail?id=${dynasty.id}`,
    });
  };

  return (
    <View className="dynasty-list-page">
      {/* 页面头部 */}
      <View className="page-header">
        <View className="header-content">
          <Text className="page-title">王朝管理</Text>
          <Text className="page-subtitle">管理你创建的王朝</Text>
        </View>
        <Button
          color="primary"
          size="small"
          icon={<Plus size="20rpx" />}
          onClick={handleCreateDynasty}
        >
          创建王朝
        </Button>
      </View>

      {/* 王朝列表 */}
      {loading ? (
        <LoadingState text="加载中..." />
      ) : !isLoggedIn ? (
        <EmptyState
          title="请先登录"
          description="登录后查看您的王朝信息"
          actionText="立即登录"
          onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
        />
      ) : dynasties.length > 0 ? (
        <View className="dynasty-list">
          {dynasties.map((dynasty) => (
            <PageCard
              key={dynasty.id}
              title={dynasty.name}
              description={`编码: ${dynasty.code} · ${
                dynasty.serverId
              }区 · 创建于${formatDate(dynasty.createdAt)}`}
              icon={<Star size="40rpx" color="#1976d2" />}
              extra={
                <View className="dynasty-status">
                  <Text
                    className={`status-badge ${
                      dynasty.grabEnabled ? "enabled" : "disabled"
                    }`}
                  >
                    {dynasty.grabEnabled ? "抢夺开启" : "抢夺关闭"}
                  </Text>
                </View>
              }
              onClick={() => handleDynastyDetail(dynasty)}
            />
          ))}
        </View>
      ) : (
        <EmptyState
          title="暂无王朝"
          description="创建王朝开始管理官职抢夺"
          actionText="创建王朝"
          onAction={handleCreateDynasty}
        />
      )}
    </View>
  );
};

export default DynastyList;
