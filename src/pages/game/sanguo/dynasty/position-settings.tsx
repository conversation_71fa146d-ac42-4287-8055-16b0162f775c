import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { Button, DatetimePicker, Checkbox, Popup } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { positionApi, dynastyApi } from "@/services/api";
import {
  showError,
  showSuccess,
  formatDateTimeForAPI,
  formatDateForAPI,
} from "@/utils";
import LoadingState from "@/components/LoadingState";
import "./position-settings.scss";

const PositionSettings: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);

  const [selectedPosition, setSelectedPosition] = useState<
    "TAI_WEI" | "SHANG_SHU_LING"
  >("TAI_WEI");
  const [formData, setFormData] = useState({
    grabStartTime: "",
    grabEndTime: "",
    dutyDate: "",
    disabledTimeSlots: "",
  });

  // 时间选择器状态
  const [showDutyDatePicker, setShowDutyDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // 当前选择的时间
  const [currentDutyDate, setCurrentDutyDate] = useState(new Date());
  const [currentStartTime, setCurrentStartTime] = useState(new Date());
  const [currentEndTime, setCurrentEndTime] = useState(new Date());

  const dynastyId = router.params.dynastyId;

  // 加载配置数据
  const loadPositionConfig = async (
    positionType: "TAI_WEI" | "SHANG_SHU_LING"
  ) => {
    if (!dynastyId) return;

    try {
      setLoading(true);
      // 获取王朝详情，从中提取官职配置
      const response = await dynastyApi.getById(dynastyId);
      if (response.data && response.data.positions) {
        const position = response.data.positions.find(
          (p: any) => p.positionType === positionType
        );

        if (position) {
          setFormData({
            grabStartTime: position.grabStartTime || "",
            grabEndTime: position.grabEndTime || "",
            dutyDate: position.dutyDate || "",
            disabledTimeSlots: position.disabledTimeSlots || "",
          });

          // 设置当前选择的时间
          if (position.dutyDate) {
            setCurrentDutyDate(new Date(position.dutyDate));
          }
          if (position.grabStartTime) {
            setCurrentStartTime(new Date(position.grabStartTime));
          }
          if (position.grabEndTime) {
            setCurrentEndTime(new Date(position.grabEndTime));
          }
        } else {
          // 如果没有找到对应官职，使用默认值
          setFormData({
            grabStartTime: "",
            grabEndTime: "",
            dutyDate: "",
            disabledTimeSlots: "",
          });
        }
      }
    } catch (error) {
      console.error("加载官职配置失败:", error);
      // 如果获取失败，使用默认值
      setFormData({
        grabStartTime: "",
        grabEndTime: "",
        dutyDate: "",
        disabledTimeSlots: "",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }
    if (!dynastyId) return;
    loadPositionConfig(selectedPosition);
  }, [dynastyId, selectedPosition, isLoggedIn, authLoading]);

  // 处理官职切换
  const handlePositionChange = (position: "TAI_WEI" | "SHANG_SHU_LING") => {
    setSelectedPosition(position);
    // 切换官职后会自动触发useEffect重新加载配置
  };

  // 处理任职日期选择 - 只保存日期部分
  const handleDutyDateConfirm = (date: Date) => {
    setCurrentDutyDate(date);
    setFormData({
      ...formData,
      dutyDate: formatDateForAPI(date), // 使用新的日期格式化函数
    });
    setShowDutyDatePicker(false);
  };

  // 处理抢夺开始时间选择
  const handleStartTimeConfirm = (date: Date) => {
    setCurrentStartTime(date);
    setFormData({
      ...formData,
      grabStartTime: formatDateTimeForAPI(date),
    });
    setShowStartTimePicker(false);

    // 如果结束时间不在同一天，重置结束时间
    if (currentEndTime.toDateString() !== date.toDateString()) {
      const newEndTime = new Date(date);
      newEndTime.setHours(23, 59, 59);
      setCurrentEndTime(newEndTime);
      setFormData((prev) => ({
        ...prev,
        grabEndTime: formatDateTimeForAPI(newEndTime),
      }));
    }
  };

  // 处理抢夺结束时间选择
  const handleEndTimeConfirm = (date: Date) => {
    // 确保结束时间在开始时间的同一天
    if (currentStartTime.toDateString() !== date.toDateString()) {
      showError("抢夺开始时间和结束时间必须在同一天");
      return;
    }

    // 确保结束时间晚于开始时间
    if (date <= currentStartTime) {
      showError("结束时间必须晚于开始时间");
      return;
    }

    setCurrentEndTime(date);
    setFormData({
      ...formData,
      grabEndTime: formatDateTimeForAPI(date),
    });
    setShowEndTimePicker(false);
  };

  const handleSubmit = async () => {
    if (!dynastyId) return;

    if (
      !formData.grabStartTime ||
      !formData.grabEndTime ||
      !formData.dutyDate
    ) {
      showError("请填写完整的时间设置");
      return;
    }

    try {
      await positionApi.setGrabTime(dynastyId, {
        positionType: selectedPosition,
        grabStartTime: formData.grabStartTime,
        grabEndTime: formData.grabEndTime,
        dutyDate: formData.dutyDate,
        disabledTimeSlots: formData.disabledTimeSlots
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s !== "")
          .map((s) => parseInt(s)),
      });
      showSuccess("官职设置已保存");
      // 保存成功后重新加载配置
      await loadPositionConfig(selectedPosition);
    } catch (error) {
      console.error("保存官职设置失败:", error);
    }
  };

  const handleTimeSlotToggle = (slot: number) => {
    const currentSlots = formData.disabledTimeSlots
      ? formData.disabledTimeSlots
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s !== "")
      : [];
    const slotStr = slot.toString();

    const newDisabledSlots = currentSlots.includes(slotStr)
      ? currentSlots.filter((s) => s !== slotStr)
      : [...currentSlots, slotStr];

    setFormData({ ...formData, disabledTimeSlots: newDisabledSlots.join(",") });
  };

  const renderTimeSlots = () => {
    const slots: JSX.Element[] = [];
    for (let i = 0; i < 24; i++) {
      const timeLabel = `${i.toString().padStart(2, "0")}:00-${i
        .toString()
        .padStart(2, "0")}:59`;
      const currentSlots = formData.disabledTimeSlots
        ? formData.disabledTimeSlots
            .split(",")
            .map((s) => s.trim())
            .filter((s) => s !== "")
        : [];
      const isDisabled = currentSlots.includes(i.toString());

      slots.push(
        <View key={i} className="time-slot-item">
          <Checkbox
            checked={!isDisabled}
            onChange={() => handleTimeSlotToggle(i)}
          >
            {timeLabel}
          </Checkbox>
        </View>
      );
    }
    return slots;
  };

  return (
    <View className="position-settings-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : (
        <>
          <View className="settings-container">
            <View className="settings-header">
              <Text className="settings-title">官职抢夺设置</Text>
              <Text className="settings-subtitle">
                设置太尉和尚书令的抢夺时间
              </Text>
            </View>

            {/* 官职选择 */}
            <View className="position-selector">
              <Text className="selector-title">选择官职</Text>
              <View className="position-tabs">
                <Button
                  size="small"
                  color={selectedPosition === "TAI_WEI" ? "primary" : "default"}
                  onClick={() => handlePositionChange("TAI_WEI")}
                >
                  太尉
                </Button>
                <Button
                  size="small"
                  color={
                    selectedPosition === "SHANG_SHU_LING"
                      ? "primary"
                      : "default"
                  }
                  onClick={() => handlePositionChange("SHANG_SHU_LING")}
                >
                  尚书令
                </Button>
              </View>
            </View>

            {/* 时间设置 */}
            <View className="time-settings">
              <Text className="section-title">时间设置</Text>

              {/* 任职日期 */}
              <View className="time-setting-item">
                <Text className="time-label">任职日期：</Text>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setShowDutyDatePicker(true)}
                >
                  {formData.dutyDate || "点击选择日期"}
                </Button>
              </View>

              {/* 抢夺开始时间 */}
              <View className="time-setting-item">
                <Text className="time-label">抢夺开始时间：</Text>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setShowStartTimePicker(true)}
                >
                  {formData.grabStartTime || "点击选择时间"}
                </Button>
              </View>

              {/* 抢夺结束时间 */}
              <View className="time-setting-item">
                <Text className="time-label">抢夺结束时间：</Text>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => setShowEndTimePicker(true)}
                >
                  {formData.grabEndTime || "点击选择时间"}
                </Button>
              </View>
            </View>

            {/* 快速设置 */}
            <View className="quick-settings">
              <Text className="quick-title">快速设置</Text>
              <View className="quick-buttons">
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const today = new Date();
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);

                    const startTime = new Date();
                    const endTime = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2小时后

                    setFormData((prev) => ({
                      ...prev,
                      dutyDate: formatDateForAPI(tomorrow), // 使用新的日期格式化函数
                      grabStartTime: formatDateTimeForAPI(startTime),
                      grabEndTime: formatDateTimeForAPI(endTime),
                      disabledTimeSlots: prev.disabledTimeSlots,
                    }));

                    setCurrentDutyDate(tomorrow);
                    setCurrentStartTime(startTime);
                    setCurrentEndTime(endTime);
                  }}
                >
                  明日任职
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const today = new Date();
                    const nextWeek = new Date(today);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    nextWeek.setHours(0, 0, 0, 0);

                    const startTime = new Date();
                    const endTime = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2小时后

                    setFormData((prev) => ({
                      ...prev,
                      dutyDate: formatDateForAPI(nextWeek), // 使用新的日期格式化函数
                      grabStartTime: formatDateTimeForAPI(startTime),
                      grabEndTime: formatDateTimeForAPI(endTime),
                      disabledTimeSlots: prev.disabledTimeSlots,
                    }));

                    setCurrentDutyDate(nextWeek);
                    setCurrentStartTime(startTime);
                    setCurrentEndTime(endTime);
                  }}
                >
                  下周任职
                </Button>
              </View>
            </View>

            {/* 时段设置 */}
            <View className="time-slots-section">
              <Text className="section-title">可抢夺时段</Text>
              <Text className="section-desc">取消勾选的时段将不允许抢夺</Text>
              <View className="time-slots-grid">{renderTimeSlots()}</View>
            </View>

            {/* 操作按钮 */}
            <View className="settings-actions">
              <Button color="primary" block onClick={handleSubmit}>
                保存设置
              </Button>
            </View>

            {/* 说明信息 */}
            <View className="settings-tips">
              <Text className="tip-title">设置说明：</Text>
              <Text className="tip-text">
                • 设置新的抢夺时间会清空该官职的历史抢夺数据
              </Text>
              <Text className="tip-text">• 抢夺时间内成员才能进行官职抢夺</Text>
              <Text className="tip-text">• 可以禁用特定时段防止抢夺冲突</Text>
              <Text className="tip-text">• 每个时段只能有一个成员任职</Text>
            </View>
          </View>

          {/* 任职日期选择器 */}
          <Popup
            open={showDutyDatePicker}
            onClose={() => setShowDutyDatePicker(false)}
            rounded
            placement="bottom"
            style={{ height: "50%" }}
          >
            <DatetimePicker
              type="date"
              value={currentDutyDate}
              onConfirm={handleDutyDateConfirm}
              onCancel={() => setShowDutyDatePicker(false)}
              formatter={(type, val) => {
                if (type === "year") {
                  return `${val}年`;
                }
                if (type === "month") {
                  return `${val}月`;
                }
                if (type === "day") {
                  return `${val}日`;
                }
                return val;
              }}
            >
              <DatetimePicker.Toolbar>
                <DatetimePicker.Button>取消</DatetimePicker.Button>
                <DatetimePicker.Title>选择任职日期</DatetimePicker.Title>
                <DatetimePicker.Button>确认</DatetimePicker.Button>
              </DatetimePicker.Toolbar>
            </DatetimePicker>
          </Popup>

          {/* 抢夺开始时间选择器 */}
          <Popup
            open={showStartTimePicker}
            rounded
            placement="bottom"
            style={{ height: "50%" }}
            onClose={() => setShowStartTimePicker(false)}
          >
            <DatetimePicker
              type="datetime"
              value={currentStartTime}
              onConfirm={handleStartTimeConfirm}
              onCancel={() => setShowStartTimePicker(false)}
              formatter={(type, val) => {
                if (type === "year") {
                  return `${val}年`;
                }
                if (type === "month") {
                  return `${val}月`;
                }
                if (type === "day") {
                  return `${val}日`;
                }
                if (type === "hour") {
                  return `${val}时`;
                }
                if (type === "minute") {
                  return `${val}分`;
                }
                if (type === "second") {
                  return `${val}秒`;
                }
                return val;
              }}
            >
              <DatetimePicker.Toolbar>
                <DatetimePicker.Button>取消</DatetimePicker.Button>
                <DatetimePicker.Title>选择开始时间</DatetimePicker.Title>
                <DatetimePicker.Button>确认</DatetimePicker.Button>
              </DatetimePicker.Toolbar>
            </DatetimePicker>
          </Popup>

          {/* 抢夺结束时间选择器 */}
          <Popup
            open={showEndTimePicker}
            rounded
            placement="bottom"
            style={{ height: "50%" }}
            onClose={() => setShowEndTimePicker(false)}
          >
            <DatetimePicker
              type="datetime"
              value={currentEndTime}
              onConfirm={handleEndTimeConfirm}
              onCancel={() => setShowEndTimePicker(false)}
              formatter={(type, val) => {
                if (type === "year") {
                  return `${val}年`;
                }
                if (type === "month") {
                  return `${val}月`;
                }
                if (type === "day") {
                  return `${val}日`;
                }
                if (type === "hour") {
                  return `${val}时`;
                }
                if (type === "minute") {
                  return `${val}分`;
                }
                if (type === "second") {
                  return `${val}秒`;
                }
                return val;
              }}
            >
              <DatetimePicker.Toolbar>
                <DatetimePicker.Button>取消</DatetimePicker.Button>
                <DatetimePicker.Title>选择结束时间</DatetimePicker.Title>
                <DatetimePicker.Button>确认</DatetimePicker.Button>
              </DatetimePicker.Toolbar>
            </DatetimePicker>
          </Popup>
        </>
      )}
    </View>
  );
};

export default PositionSettings;
