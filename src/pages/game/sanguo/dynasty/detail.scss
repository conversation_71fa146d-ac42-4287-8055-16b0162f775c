.dynasty-detail-page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .dynasty-info {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .dynasty-header {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;

      .dynasty-icon {
        width: 80rpx;
        height: 80rpx;
        background: rgba(25, 118, 210, 0.1);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        flex-shrink: 0;
      }

      .dynasty-basic {
        flex: 1;

        .dynasty-name {
          display: block;
          font-size: 36rpx;
          font-weight: 600;
          color: #212121;
          margin-bottom: 8rpx;
        }

        .dynasty-code {
          display: block;
          font-size: 28rpx;
          color: #616161;
          margin-bottom: 4rpx;
        }

        .dynasty-server {
          display: block;
          font-size: 28rpx;
          color: #616161;
        }
      }
    }

    .dynasty-meta {
      padding-top: 24rpx;
      border-top: 1rpx solid #e3f2fd;

      .meta-item {
        display: block;
        font-size: 28rpx;
        color: #616161;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .grab-control {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);

    .control-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .control-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #212121;
      }
    }

    .control-desc {
      font-size: 28rpx;
      color: #616161;
    }
  }

  .dynasty-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .dynasty-basic {
      flex: 1;

      .dynasty-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 8rpx;
        display: block;
      }

      .dynasty-code-row {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .dynasty-code {
          font-size: 28rpx;
          color: #616161;
          margin-right: 16rpx;
        }
      }

      .dynasty-server {
        font-size: 28rpx;
        color: #616161;
      }
    }

    .dynasty-actions {
      display: flex;
      flex-direction: column;
      gap: 16rpx;
      height: 100%;
      justify-content: center;
      min-height: 120rpx;

      .taroify-button {
        height: 56rpx;
        font-size: 26rpx;
        min-width: 120rpx;
      }
    }
  }

  .dynasty-actions {
    display: flex;
    gap: 16rpx;
    flex-wrap: wrap;
    margin-bottom: 32rpx;

    .taroify-button {
      flex: 1;
      min-width: 200rpx;
    }
  }

  .grab-results {
    margin-top: 32rpx;

    .results-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .results-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #212121;
      }

      .date-selector {
        display: flex;
        gap: 8rpx;

        .taroify-button {
          font-size: 24rpx;
          padding: 8rpx 16rpx;
        }
      }
    }

    .position-results {
      margin-bottom: 32rpx;

      .position-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 16rpx;
        display: block;
      }

      .time-slots-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12rpx;

        .time-slot {
          background: #ffffff;
          border-radius: 8rpx;
          padding: 12rpx 8rpx;
          text-align: center;
          border: 1rpx solid #e3f2fd;
          min-height: 80rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;

          &.available {
            border-color: #1976d2;
            background: rgba(25, 118, 210, 0.05);
          }

          &.occupied {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;

            .slot-status {
              color: #2e7d32;
              font-weight: 600;
            }
          }

          &.disabled {
            background: #f5f5f5;
            border-color: #bdbdbd;
            opacity: 0.6;
          }

          .slot-time {
            font-size: 22rpx;
            font-weight: 600;
            color: #212121;
            margin-bottom: 4rpx;
            display: block;
          }

          .slot-status {
            font-size: 20rpx;
            color: #616161;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .error-state {
    text-align: center;
    padding: 120rpx 32rpx;
    color: #616161;
    font-size: 28rpx;
  }

  // 账号名称对话框样式
  .account-name-content {
    padding: 16rpx 0;

    .account-name-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .account-name-text {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      word-break: break-all;
      line-height: 1.4;
    }
  }
}
