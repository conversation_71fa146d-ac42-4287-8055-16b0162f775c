.account-detail-page {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .account-info {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .account-header {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;

      .account-icon {
        margin-right: 24rpx;
      }

      .account-basic {
        flex: 1;

        .account-name {
          display: block;
          font-size: 36rpx;
          font-weight: 600;
          color: var(--text-color);
          margin-bottom: 8rpx;
        }

        .account-server,
        .account-tier {
          display: block;
          font-size: 28rpx;
          color: var(--text-color-secondary);
          margin-bottom: 4rpx;
        }
      }
    }

    .account-stats {
      display: flex;
      justify-content: space-between;
      padding: 24rpx 0;
      border-top: 1rpx solid var(--border-color);

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-value {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: var(--primary-color);
          margin-bottom: 8rpx;
        }

        .stat-label {
          display: block;
          font-size: 24rpx;
          color: var(--text-color-secondary);
        }
      }
    }
  }

  .alliance-section {
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 32rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: var(--text-color);
      }
    }

    .war-applications {
      margin-top: 32rpx;
      background: white;
      border-radius: 16rpx;
      padding-left: 32rpx;
      padding-right: 32rpx;
      padding-top: 1rpx;
      padding-bottom: 33rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      .section-header {
        margin-bottom: 24rpx;
      }

      .war-apply-buttons {
        display: flex;
        gap: 16rpx;
        justify-content: center;
      }
    }

    .war-arrangements {
      margin-top: 32rpx;
      background: white;
      border-radius: 16rpx;
      padding-left: 32rpx;
      padding-right: 32rpx;
      padding-top: 1rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      .section-header {
        margin-bottom: 24rpx;
        padding-bottom: 16rpx;
        border-bottom: 1rpx solid var(--border-color);

        .section-title {
          font-size: 32rpx;
          font-weight: 600;
          color: var(--text-color);
        }
      }

      .war-arrangement-card {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 16rpx;
        border-left: 4rpx solid #667eea;

        &:last-child {
          margin-bottom: 0;
        }

        .arrangement-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16rpx;

          .war-type {
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
          }

          .arrangement-status {
            font-size: 24rpx;
            color: var(--success-color);
            font-weight: 500;
            background: #f0f9ff;
            padding: 4rpx 12rpx;
            border-radius: 8rpx;
          }
        }

        .group-details {
          .group-info {
            margin-bottom: 20rpx;

            .group-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12rpx;

              .group-name {
                font-size: 28rpx;
                font-weight: 700;
                color: #667eea;
                flex: 1;
              }

              .member-count {
                font-size: 22rpx;
                color: #999;
                background: #f0f0f0;
                padding: 4rpx 12rpx;
                border-radius: 12rpx;
                font-weight: 500;
              }
            }

            .group-task {
              background: #f8f9fa;
              padding: 16rpx;
              border-radius: 12rpx;
              border-left: 4rpx solid #667eea;

              .task-label {
                font-size: 22rpx;
                color: #667eea;
                font-weight: 600;
                margin-bottom: 6rpx;
              }

              .task-content {
                font-size: 24rpx;
                color: #333;
                line-height: 1.6;
              }
            }
          }

          .group-members {
            .members-title {
              font-size: 24rpx;
              color: #333;
              margin-bottom: 16rpx;
              font-weight: 600;
              padding-bottom: 8rpx;
              border-bottom: 2rpx solid #f0f0f0;
            }

            .members-grid {
              display: flex;
              flex-direction: column;
              gap: 12rpx;
            }

            .member-item {
              display: flex;
              align-items: center;
              background: white;
              padding: 16rpx;
              border-radius: 12rpx;
              border: 1rpx solid #e8e8e8;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);

              .member-avatar {
                width: 64rpx;
                height: 64rpx;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea, #4987b9);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16rpx;
                flex-shrink: 0;

                .avatar-text {
                  color: white;
                  font-size: 24rpx;
                  font-weight: 600;
                }
              }

              .member-info {
                flex: 1;

                .member-name {
                  font-size: 26rpx;
                  color: #333;
                  margin-bottom: 6rpx;
                  font-weight: 600;
                }

                .member-stats {
                  display: flex;
                  gap: 16rpx;

                  .member-power {
                    font-size: 22rpx;
                    color: #666;
                  }

                  .member-bonus {
                    font-size: 22rpx;
                    color: #ff6b6b;
                    font-weight: 500;
                  }
                }
              }
            }

            .no-members {
              text-align: center;
              padding: 32rpx;
              background: #f8f9fa;
              border-radius: 12rpx;
              border: 2rpx dashed #ddd;

              .no-members-text {
                font-size: 24rpx;
                color: #999;
              }
            }
          }
        }

        .mobile-member {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 32rpx;
          background: linear-gradient(135deg, #687ee4, #2788d8);
          border-radius: 16rpx;
          color: white;

          .mobile-icon {
            font-size: 48rpx;
            margin-right: 20rpx;
          }

          .mobile-info {
            text-align: left;

            .mobile-text {
              font-size: 28rpx;
              font-weight: 700;
              color: white;
              margin-bottom: 6rpx;
              text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
            }

            .mobile-desc {
              font-size: 22rpx;
              color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }
    }

    .war-application-history {
      margin-top: 32rpx;
      background: white;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      .section-header {
        padding: 24rpx 32rpx 16rpx;
        border-bottom: 1rpx solid var(--border-color);
        margin-bottom: 0;
      }

      .application-status {
        font-size: 24rpx;
        font-weight: 500;

        &.status-pending {
          color: var(--warning-color);
        }

        &.status-approved {
          color: var(--success-color);
        }

        &.status-rejected {
          color: var(--danger-color);
        }
      }
    }
  }

  .no-alliance-section {
    background: white;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .no-dynasty-section {
    background: white;
    border-radius: 16rpx;
    padding: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .no-dynasty-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 12rpx;
    }
  }

  .action-buttons {
    margin-top: 40rpx;
    display: flex;
    justify-content: center;
  }
}

// 对话框样式优化
.taroify-dialog {
  .taroify-field {
    margin-bottom: 16rpx;
  }
}

// 联盟操作按钮
.alliance-actions {
  margin-top: 16rpx;
  display: flex;
  justify-content: flex-end;
}

// 王朝信息样式
.dynasty-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 32rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-color);
    }
  }
}

// 正在申请联盟样式
.pending-alliance-section {
  .taroify-card {
    border-left: 4rpx solid var(--warning-color);
  }
}
