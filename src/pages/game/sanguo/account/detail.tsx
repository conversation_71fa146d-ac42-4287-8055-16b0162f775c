import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import {
  Button,
  Cell,
  Dialog,
  Field,
  Form,
  Input,
  Checkbox,
  Flex,
} from "@taroify/core";
import {
  User,
  Edit,
  Delete,
  ShieldOutlined,
  Star,
  Setting,
  Friends,
} from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import {
  accountApi,
  allianceApplicationApi,
  warGroupApi,
  battleApi,
  allianceMemberApi,
  allianceApi,
  dynastyApi,
  accountDynastyApi,
  allianceNotificationApi,
} from "@/services/api";
import { GameAccount, AccountWarArrangementResponse } from "@/types";
import PageCard from "@/components/PageCard";
import EmptyState from "@/components/EmptyState";
import LvbuStarLevel from "@/components/LvbuStarLevel";
import {
  formatPower,
  formatRallyCapacity,
  showError,
  showSuccess,
} from "@/utils";
import "./detail.scss";

const AccountDetail: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [account, setAccount] = useState<GameAccount | null>(null);
  const [warArrangements, setWarArrangements] = useState<any[]>([]);
  const [warApplications, setWarApplications] = useState<any[]>([]);
  const [pendingAllianceApplication, setPendingAllianceApplication] =
    useState<any>(null);
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showLeaveAllianceDialog, setShowLeaveAllianceDialog] = useState(false);
  const [allianceCode, setAllianceCode] = useState("");
  const [submitting, setSubmitting] = useState(false);

  // 王朝相关状态
  const [dynastyInfo, setDynastyInfo] = useState<any>(null);
  const [showJoinDynastyDialog, setShowJoinDynastyDialog] = useState(false);
  const [dynastyCode, setDynastyCode] = useState("");
  const [joiningDynasty, setJoiningDynasty] = useState(false);

  // 通知设置相关状态
  const [showNotificationDialog, setShowNotificationDialog] = useState(false);
  const [notificationTypes, setNotificationTypes] = useState<string[]>([]);
  const [savingNotification, setSavingNotification] = useState(false);

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    const { id } = router.params;
    if (id) {
      loadAccountDetail(id);
    }
  }, [isLoggedIn, authLoading]);

  // 监听页面显示，用于从编辑页面返回时刷新数据
  useDidShow(() => {
    const { id } = router.params;
    if (id && account) {
      // 只有在已经加载过账号数据的情况下才刷新
      loadAccountDetail(id);
    }
  });

  const loadAccountDetail = async (id: string) => {
    try {
      setLoading(true);
      const response = await accountApi.getById(id);
      console.log("账号详情:", response.data);
      setAccount(response.data);

      // 初始化通知设置
      if ((response.data as any).notificationTypes) {
        setNotificationTypes((response.data as any).notificationTypes);
      }

      // 如果账号加入了王朝，加载王朝信息
      if ((response.data as any).dynastyId) {
        loadDynastyInfo((response.data as any).dynastyId);
      } else {
        setDynastyInfo(null);
      }

      // 如果账号加入了联盟，加载战事安排
      if (response.data.allianceId) {
        loadWarArrangements(id); // 传入账号ID而不是联盟ID
      }

      // 加载战事申请历史
      loadWarApplications(id);

      // 如果未加入联盟，加载联盟申请历史
      if (!response.data.allianceId) {
        loadAllianceApplications(id);
      }
    } catch (error) {
      console.error("加载账号详情失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadDynastyInfo = async (dynastyId: string) => {
    try {
      const response = await dynastyApi.getById(dynastyId);
      setDynastyInfo(response.data);
    } catch (error) {
      console.error("加载王朝信息失败:", error);
    }
  };

  const handleJoinDynasty = async () => {
    if (!dynastyCode.trim() || !account) {
      showError("请输入王朝编码");
      return;
    }

    try {
      setJoiningDynasty(true);
      // 调用账号加入王朝接口
      await accountDynastyApi.join(account.id, {
        dynastyCode: dynastyCode.trim(),
      });
      showSuccess("成功加入王朝");
      setShowJoinDynastyDialog(false);
      setDynastyCode("");
      // 重新加载账号信息
      const { id } = router.params;
      if (id) {
        loadAccountDetail(id);
      }
    } catch (error) {
    } finally {
      setJoiningDynasty(false);
    }
  };

  const handleLeaveDynasty = async () => {
    if (!account || !(account as any)?.dynastyId) return;

    try {
      await accountDynastyApi.leave(account.id);
      setDynastyInfo(null);
      showSuccess("已退出王朝");
    } catch (error) {
      console.error("退出王朝失败:", error);
    }
  };

  // 打开通知设置对话框
  const handleOpenNotificationSettings = () => {
    if (!account) return;
    setShowNotificationDialog(true);
  };

  // 保存通知设置
  const handleSaveNotificationSettings = async () => {
    if (!account) return;

    try {
      setSavingNotification(true);
      await allianceNotificationApi.updateNotificationSettings({
        accountId: account.id,
        notificationTypes,
      });
      showSuccess("通知设置已保存");
      setShowNotificationDialog(false);
      // 重新加载账号信息
      loadAccountDetail(account.id);
    } catch (error) {
      console.error("保存通知设置失败:", error);
      showError("保存通知设置失败");
    } finally {
      setSavingNotification(false);
    }
  };

  // 切换通知类型
  const handleToggleNotificationType = (type: string) => {
    if (notificationTypes.includes(type)) {
      setNotificationTypes(notificationTypes.filter((t) => t !== type));
    } else {
      setNotificationTypes([...notificationTypes, type]);
    }
  };

  const handleViewDynasty = () => {
    if (dynastyInfo?.dynasty?.id && account) {
      // 如果王朝开启了抢夺，进入抢夺页面，否则也进入抢夺页面（显示未开启提示）
      Taro.navigateTo({
        url: `/pages/game/sanguo/dynasty/position-grab?dynastyId=${dynastyInfo.dynasty.id}&accountId=${account.id}`,
      });
    }
  };

  const loadWarArrangements = async (accountId: string) => {
    try {
      // 使用获取账号的战事安排详情接口
      const response = await warGroupApi.getAccountArrangementDetail(accountId);
      const arrangements: any[] = [];

      const responseData: AccountWarArrangementResponse = response.data;

      if (
        responseData &&
        responseData.warArrangements &&
        responseData.warArrangements.length > 0
      ) {
        console.log("开始处理战事安排数据...");
        responseData.warArrangements.forEach((arrangement, index) => {
          console.log(`处理第${index + 1}个安排:`, arrangement);
          const processedArrangement = {
            id: `${accountId}-${arrangement.warType}-${
              arrangement.warGroup?.groupId || "mobile"
            }`,
            accountId: accountId,
            allianceId: responseData.account?.allianceId,
            warType: arrangement.warType,
            isMobile: arrangement.isMobile,
            warGroupId: arrangement.warGroup?.groupId,
            account: responseData.account,
            warGroup: arrangement.warGroup
              ? {
                  id: arrangement.warGroup.groupId,
                  groupName: arrangement.warGroup.groupName,
                  groupTask: arrangement.warGroup.groupTask,
                  members: arrangement.warGroup.members || [],
                }
              : null,
          };
          console.log(`处理后的第${index + 1}个安排:`, processedArrangement);
          arrangements.push(processedArrangement);
        });
      } else {
        console.log("没有战事安排数据或数据为空");
      }

      console.log("最终处理后的战事安排数组:", arrangements);
      console.log("最终数组长度:", arrangements.length);
      console.log("=== 调试信息结束 ===");

      setWarArrangements(arrangements);
    } catch (error) {
      console.error("加载账号战事安排详情失败:", error);
      setWarArrangements([]);
    }
  };

  const loadWarApplications = async (accountId: string) => {
    try {
      const response = await battleApi.getAccountApplications(accountId);
      console.log("战事申请历史:", response.data);
      setWarApplications(response.data);
    } catch (error) {
      console.error("加载战事申请历史失败:", error);
    }
  };

  // 加载联盟申请历史
  const loadAllianceApplications = async (accountId: string) => {
    try {
      const response = await allianceApplicationApi.getByAccount(accountId);
      const pendingApp = response.data.find(
        (app: any) => app.status === "PENDING"
      );

      if (pendingApp) {
        // 获取联盟详情
        const allianceResponse = await allianceApi.getById(
          pendingApp.allianceId
        );
        setPendingAllianceApplication({
          ...pendingApp,
          alliance: allianceResponse.data,
        });
      } else {
        setPendingAllianceApplication(null);
      }
    } catch (error) {
      console.error("加载联盟申请历史失败:", error);
      setPendingAllianceApplication(null);
    }
  };

  const handleEditAccount = () => {
    if (account) {
      Taro.navigateTo({
        url: `/pages/game/sanguo/account/form?id=${account.id}`,
      });
    }
  };

  const handleDeleteAccount = async () => {
    if (!account) return;

    try {
      setSubmitting(true);
      await accountApi.delete(account.id);
      showSuccess("账号删除成功");
      Taro.navigateBack();
    } catch (error) {
    } finally {
      setSubmitting(false);
      setShowDeleteDialog(false);
    }
  };

  // 退出联盟
  const handleLeaveAlliance = async () => {
    if (!account) return;

    try {
      setSubmitting(true);
      await allianceMemberApi.removeMember(account.id);
      showSuccess("已退出联盟");
      setShowLeaveAllianceDialog(false);
      // 重新加载账号数据
      loadAccountDetail(account.id);
    } catch (error) {
      console.error("退出联盟失败:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleJoinAlliance = async () => {
    if (!account || !allianceCode.trim()) {
      showError("请输入联盟编码");
      return;
    }

    try {
      setSubmitting(true);
      await allianceApplicationApi.apply({
        accountId: account.id,
        allianceCode: allianceCode.trim(),
      });
      showSuccess("申请已提交");
      setShowJoinDialog(false);
      setAllianceCode("");

      // 重新加载账号详情和联盟申请历史
      loadAccountDetail(account.id);
      loadAllianceApplications(account.id);
    } catch (error) {
      console.error("申请加入联盟失败:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleApplyWar = async (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    if (!account) return;

    try {
      setSubmitting(true);
      await battleApi.apply({
        accountId: account.id,
        warType,
      });
      showSuccess(`申请加入${getWarTypeName(warType)}成功`);
      // 重新加载申请历史
      loadWarApplications(account.id);
    } catch (error) {
      console.error("申请战事失败:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancelWarApplication = async (
    warType: "GUANDU_ONE" | "GUANDU_TWO"
  ) => {
    if (!account) return;

    try {
      setSubmitting(true);
      await battleApi.cancelApplication(account.id, warType);
      showSuccess(`已取消申请${getWarTypeName(warType)}`);
      // 重新加载申请历史
      loadWarApplications(account.id);
    } catch (error) {
      console.error("取消申请失败:", error);
    } finally {
      setSubmitting(false);
    }
  };

  // 处理战事申请/取消申请
  const handleWarAction = async (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    if (!account) return;

    // 检查是否已有战事安排
    const hasArrangement = warArrangements.some(
      (arr) => arr.warType === warType
    );
    if (hasArrangement) {
      showError(`已加入${getWarTypeName(warType)}，无法执行此操作`);
      return;
    }

    // 检查是否已有待处理的申请
    const pendingApplication = warApplications.find(
      (app) => app.warType === warType && app.status === "PENDING"
    );

    if (pendingApplication) {
      // 如果有待处理申请，则取消申请
      await handleCancelWarApplication(warType);
    } else {
      // 如果没有待处理申请，则提交申请
      await handleApplyWar(warType);
      const { id } = router.params;
      id && loadWarArrangements(id);
    }
  };

  // 获取按钮文本
  const getWarButtonText = (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    // 检查是否已有战事安排
    const hasArrangement = warArrangements.some(
      (arr) => arr.warType === warType
    );
    if (hasArrangement) {
      return `已加入${getWarTypeName(warType)}`;
    }

    const pendingApplication = warApplications.find(
      (app) => app.warType === warType && app.status === "PENDING"
    );

    if (pendingApplication) {
      return `取消申请${getWarTypeName(warType)}`;
    }

    return `申请${getWarTypeName(warType)}`;
  };

  // 获取按钮颜色
  const getWarButtonColor = (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    const pendingApplication = warApplications.find(
      (app) => app.warType === warType && app.status === "PENDING"
    );

    if (pendingApplication) {
      return "danger" as const;
    }

    return "primary" as const;
  };

  // 获取按钮禁用状态
  const getWarButtonDisabled = (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    if (!account?.alliance) return true; // 必须加入联盟才能申请战事

    // 检查是否已有战事安排
    const hasCurrentArrangement = warArrangements.some(
      (arr) => arr.warType === warType
    );
    const otherWarType = warType === "GUANDU_ONE" ? "GUANDU_TWO" : "GUANDU_ONE";
    const hasOtherArrangement = warArrangements.some(
      (arr) => arr.warType === otherWarType
    );

    // 如果已有当前战事安排，禁用按钮
    if (hasCurrentArrangement) {
      return true;
    }

    // 如果已有另一个官渡战事安排，禁用当前按钮
    if (hasOtherArrangement) {
      return true;
    }

    // 检查申请状态的互斥逻辑
    const hasOtherWarApplication = warApplications.some(
      (app) => app.warType === otherWarType && app.status === "PENDING"
    );

    // 如果已申请另一个官渡战事，则禁用当前按钮（除非当前也有申请，可以取消）
    const currentApplication = warApplications.find(
      (app) => app.warType === warType && app.status === "PENDING"
    );
    return hasOtherWarApplication && !currentApplication;
  };

  const getWarTypeName = (warType: string) => {
    switch (warType) {
      case "GUANDU_ONE":
        return "官渡一";
      case "GUANDU_TWO":
        return "官渡二";
      case "SIEGE":
        return "攻城";
      case "DEFENSE":
        return "守城";
      default:
        return "未知";
    }
  };

  const getMemberTierName = (tier: string) => {
    return tier ? `${tier.replace("TIER_", "")}阶` : "1阶";
  };

  // 获取战事申请状态文本
  const getWarApplicationStatus = (warType: "GUANDU_ONE" | "GUANDU_TWO") => {
    const application = warApplications.find((app) => app.warType === warType);
    if (!application) return null;

    switch (application.status) {
      case "PENDING":
        return "待审核";
      case "APPROVED":
        return "已加入";
      case "REJECTED":
        return "已拒绝";
      default:
        return null;
    }
  };

  if (!account && !loading) {
    return (
      <EmptyState
        title="账号不存在"
        description="该账号可能已被删除"
        actionText="返回列表"
        onAction={() => Taro.navigateBack()}
      />
    );
  }

  return (
    <View className="account-detail-page">
      {loading ? (
        <View className="loading-placeholder">
          <EmptyState title="加载中" description="数据加载中" />
        </View>
      ) : account ? (
        <>
          {/* 账号基本信息 */}
          <View className="account-info">
            <View className="account-header">
              <View className="account-icon">
                <User size="60rpx" color="var(--primary-color)" />
              </View>
              <View className="account-basic">
                <Text className="account-name">{account.accountName}</Text>
                <Text className="account-server">
                  区服: {account.serverId}区
                </Text>
                <Text className="account-tier">
                  阶位: {getMemberTierName(account.memberTier)}
                </Text>
              </View>
              <Button
                size="small"
                color="primary"
                variant="outlined"
                icon={<Edit />}
                onClick={handleEditAccount}
              >
                编辑
              </Button>
            </View>

            <View className="account-stats">
              <View className="stat-item">
                <Text className="stat-value">
                  {formatPower(account.powerValue)}
                </Text>
                <Text className="stat-label">战力</Text>
              </View>
              <View className="stat-item">
                <Text className="stat-value">{account.troopLevel || 0}</Text>
                <Text className="stat-label">兵等级</Text>
              </View>
              <View className="stat-item">
                <Text className="stat-value">{account.damageBonus || 0}%</Text>
                <Text className="stat-label">加成</Text>
              </View>
              <View className="stat-item">
                <Text className="stat-value">
                  {account.rallyCapacity
                    ? formatRallyCapacity(account.rallyCapacity)
                    : "-"}
                </Text>
                <Text className="stat-label">集结容量</Text>
              </View>
              <View className="stat-item">
                <View className="stat-value">
                  <LvbuStarLevel
                    level={account.lvbuStarLevel || 0}
                    size="small"
                  />
                </View>
                <Text className="stat-label">吕布星级</Text>
              </View>
            </View>
          </View>

          {/* 王朝信息 */}
          {dynastyInfo ? (
            <View className="dynasty-section">
              <View className="section-header">
                <Text className="section-title">王朝信息</Text>
              </View>
              <PageCard
                title={dynastyInfo.dynasty.name}
                description={`编码: ${dynastyInfo.dynasty.code} · 区服: ${
                  dynastyInfo.dynasty.serverId
                }区\n官职抢夺: ${
                  dynastyInfo.dynasty.grabEnabled ? "开启" : "关闭"
                }`}
                icon={<Star size="40rpx" color="#ff9800" />}
                extra={
                  <Button
                    size="small"
                    color="danger"
                    variant="outlined"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLeaveDynasty();
                    }}
                  >
                    退出王朝
                  </Button>
                }
                onClick={handleViewDynasty}
              />
            </View>
          ) : (
            <View className="no-dynasty-section">
              <Cell
                title="未加入王朝"
                titleClass="no-dynasty-title"
                brief="当前账号还未加入王朝"
              >
                <Button
                  size="small"
                  color="primary"
                  onClick={() => setShowJoinDynastyDialog(true)}
                >
                  加入王朝
                </Button>
              </Cell>
            </View>
          )}

          {/* 联盟信息 */}
          {account.alliance ? (
            <View className="alliance-section">
              <PageCard
                title={account.alliance.name}
                description={`编码: ${account.alliance.code} · 区服: ${account.alliance.serverId}区`}
                icon={<ShieldOutlined size="40rpx" color="#1976d2" />}
                extra={
                  <View className="alliance-actions">
                    <Button
                      size="small"
                      color="primary"
                      variant="outlined"
                      icon={<Setting />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenNotificationSettings();
                      }}
                      style={{ marginRight: "16rpx" }}
                    >
                      设置
                    </Button>
                    <Button
                      size="small"
                      color="danger"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowLeaveAllianceDialog(true);
                      }}
                    >
                      退出联盟
                    </Button>
                  </View>
                }
              />

              {/* 战事申请 */}
              <View className="war-applications">
                <View className="section-header">
                  <Text className="section-title">战事申请</Text>
                </View>
                <View className="war-apply-buttons">
                  <Button
                    size="small"
                    color={getWarButtonColor("GUANDU_ONE")}
                    variant="contained"
                    disabled={getWarButtonDisabled("GUANDU_ONE") || submitting}
                    loading={submitting}
                    onClick={() => handleWarAction("GUANDU_ONE")}
                    style={{ marginRight: "16rpx" }}
                  >
                    {getWarButtonText("GUANDU_ONE")}
                  </Button>
                  <Button
                    size="small"
                    color={getWarButtonColor("GUANDU_TWO")}
                    variant="contained"
                    disabled={getWarButtonDisabled("GUANDU_TWO") || submitting}
                    loading={submitting}
                    onClick={() => handleWarAction("GUANDU_TWO")}
                  >
                    {getWarButtonText("GUANDU_TWO")}
                  </Button>
                </View>
              </View>

              {/* 战事安排 */}
              {warArrangements.length > 0 && (
                <View className="war-arrangements">
                  <View className="section-header">
                    <Text className="section-title">
                      战事安排 (数量: {warArrangements.length})
                    </Text>
                  </View>
                  {warArrangements.map((arrangement) => (
                    <View key={arrangement.id} className="war-arrangement-card">
                      <View className="arrangement-header">
                        <Text className="war-type">
                          {getWarTypeName(arrangement.warType)}
                        </Text>
                        <Text className="arrangement-status">已安排</Text>
                      </View>

                      {arrangement.warGroup ? (
                        <View className="group-details">
                          <View className="group-info">
                            <View className="group-header">
                              <Text className="group-name">
                                📁 {arrangement.warGroup.groupName}
                              </Text>
                              <Text className="member-count">
                                {arrangement.warGroup.members?.length || 0}人
                              </Text>
                            </View>
                            {arrangement.warGroup.groupTask && (
                              <View className="group-task">
                                <Text className="task-label">任务：</Text>
                                <Text className="task-content">
                                  {arrangement.warGroup.groupTask}
                                </Text>
                              </View>
                            )}
                          </View>

                          {/* 显示分组成员 */}
                          <View className="group-members">
                            <Text className="members-title">分组成员</Text>
                            {arrangement.warGroup.members &&
                            arrangement.warGroup.members.length > 0 ? (
                              <View className="members-grid">
                                {arrangement.warGroup.members.map(
                                  (member: any) => (
                                    <View
                                      key={member.id}
                                      className="member-item"
                                    >
                                      <View className="member-avatar">
                                        <Text className="avatar-text">
                                          {member.accountName?.charAt(0) || "?"}
                                        </Text>
                                      </View>
                                      <View className="member-info">
                                        <Text className="member-name">
                                          {member.accountName}
                                        </Text>
                                        <View className="member-stats">
                                          <Text className="member-power">
                                            战力:{" "}
                                            {formatPower(member.powerValue)}
                                          </Text>
                                          {member.damageBonus && (
                                            <Text className="member-bonus">
                                              加成: +{member.damageBonus}%
                                            </Text>
                                          )}
                                        </View>
                                      </View>
                                    </View>
                                  )
                                )}
                              </View>
                            ) : (
                              <View className="no-members">
                                <Text className="no-members-text">
                                  暂无其他成员
                                </Text>
                              </View>
                            )}
                          </View>
                        </View>
                      ) : (
                        <View className="mobile-member">
                          <View className="mobile-icon">🚀</View>
                          <View className="mobile-info">
                            <Text className="mobile-text">机动人员</Text>
                            <Text>{`\n`}</Text>
                            <Text className="mobile-desc">
                              随时待命，灵活调配（机灵点~！）
                            </Text>
                          </View>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              )}

              {/* 南蛮组队 */}
              <View style={{ marginTop: 20 }}>
                <PageCard
                  title="南蛮组队"
                  description="查看/加入联盟内的南蛮活动分组"
                  icon={<Friends size="40rpx" color="#ff9800" />}
                  onClick={() => {
                    if (account?.allianceId) {
                      Taro.navigateTo({
                        url: `/pages/game/sanguo/barbarian/team?accountId=${account.id}&allianceId=${account.allianceId}`,
                      });
                    } else {
                      showError("请先加入联盟");
                    }
                  }}
                />
              </View>

              {/* 战事申请历史 */}
              {warApplications.length > 0 && (
                <View className="war-application-history">
                  <View className="section-header">
                    <Text className="section-title">申请历史</Text>
                  </View>
                  {warApplications.map((application) => (
                    <Cell
                      key={application.id}
                      titleStyle={{ flex: 2 }}
                      title={getWarTypeName(application.warType)}
                      brief={`申请时间: ${application.createdAt}`}
                      rightIcon={
                        <Text
                          className={`application-status status-${application.status.toLowerCase()}`}
                        >
                          {getWarApplicationStatus(application.warType)}
                        </Text>
                      }
                    />
                  ))}
                </View>
              )}
            </View>
          ) : (
            <View className="no-alliance-section">
              {pendingAllianceApplication ? (
                <View className="pending-alliance-section">
                  <PageCard
                    title={`正在申请加入联盟【${pendingAllianceApplication.alliance.name}】`}
                    description={`编码: ${pendingAllianceApplication.alliance.code} · 区服: ${pendingAllianceApplication.alliance.serverId}区 · 申请状态: 待审核`}
                    icon={
                      <ShieldOutlined
                        size="40rpx"
                        color="var(--warning-color)"
                      />
                    }
                  />
                </View>
              ) : (
                <EmptyState
                  title="未加入联盟"
                  description="当前账号还未加入联盟"
                  actionText="申请加入联盟"
                  onAction={() => setShowJoinDialog(true)}
                />
              )}
            </View>
          )}

          {/* 操作按钮 */}
          <View className="action-buttons">
            <Button
              color="danger"
              variant="outlined"
              size="small"
              icon={<Delete />}
              onClick={() => setShowDeleteDialog(true)}
            >
              删除账号
            </Button>
          </View>

          {/* 申请加入联盟对话框 */}
          <Dialog
            open={showJoinDialog}
            onClose={() => setShowJoinDialog(false)}
            title="申请加入联盟"
          >
            <Dialog.Content>
              <Form>
                <Field label="联盟编码">
                  <Input
                    placeholder="请输入6位联盟编码"
                    value={allianceCode}
                    onChange={(e) => setAllianceCode(e.detail.value)}
                    maxlength={6}
                  />
                </Field>
              </Form>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowJoinDialog(false)}>取消</Button>
              <Button
                color="primary"
                loading={submitting}
                onClick={handleJoinAlliance}
              >
                申请加入
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 删除确认对话框 */}
          <Dialog
            open={showDeleteDialog}
            onClose={() => setShowDeleteDialog(false)}
            title="确认删除"
          >
            <Dialog.Content>
              <Text>
                确定要删除账号&quot;{account.accountName}
                &quot;吗？此操作不可撤销。
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowDeleteDialog(false)}>取消</Button>
              <Button
                color="danger"
                loading={submitting}
                onClick={handleDeleteAccount}
              >
                确认删除
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 退出联盟确认对话框 */}
          <Dialog
            open={showLeaveAllianceDialog}
            onClose={() => setShowLeaveAllianceDialog(false)}
            title="确认退出联盟"
          >
            <Dialog.Content>
              <Text>
                确定要退出联盟&quot;{account?.alliance?.name}
                &quot;吗？退出后将失去所有战事安排。
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowLeaveAllianceDialog(false)}>
                取消
              </Button>
              <Button
                color="danger"
                loading={submitting}
                onClick={handleLeaveAlliance}
              >
                确认退出
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 加入王朝对话框 */}
          <Dialog
            open={showJoinDynastyDialog}
            onClose={() => setShowJoinDynastyDialog(false)}
            title="加入王朝"
          >
            <Dialog.Content>
              <Form>
                <Field label="王朝编码">
                  <Input
                    placeholder="请输入王朝编码"
                    value={dynastyCode}
                    onChange={(e) => setDynastyCode(e.detail.value)}
                    maxlength={20}
                  />
                </Field>
              </Form>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowJoinDynastyDialog(false)}>
                取消
              </Button>
              <Button
                color="primary"
                loading={joiningDynasty}
                onClick={handleJoinDynasty}
              >
                加入王朝
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 通知设置对话框 */}
          <Dialog
            open={showNotificationDialog}
            onClose={() => setShowNotificationDialog(false)}
            title="联盟通知设置"
          >
            <Dialog.Content>
              <View>
                <View style={{ marginBottom: 12 }}>
                  <Text>选择要接收的联盟预定活动时间通知</Text>
                </View>
                <View className="notification-types">
                  <Checkbox.Group>
                    {[
                      { key: "SAN_YING_ZHAN_LV_BU", label: "三英战吕布" },
                      { key: "GUAN_DU_BAO_MING", label: "官渡报名" },
                      { key: "ZHU_JIU_LUN_YING_XIONG", label: "煮酒论英雄" },
                      { key: "NAN_MAN_RU_QIN", label: "南蛮入侵" },
                      { key: "GONG_CHENG", label: "攻城" },
                      { key: "SHOU_CHENG", label: "守城" },
                      { key: "SHUA_GONG_XUN", label: "刷功勋" },
                    ].map((type) => (
                      <Checkbox
                        checked={notificationTypes.includes(type.key)}
                        onChange={() => handleToggleNotificationType(type.key)}
                      >
                        {type.label}
                      </Checkbox>
                    ))}
                  </Checkbox.Group>
                </View>
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowNotificationDialog(false)}>
                取消
              </Button>
              <Button
                color="primary"
                loading={savingNotification}
                onClick={handleSaveNotificationSettings}
              >
                保存设置
              </Button>
            </Dialog.Actions>
          </Dialog>
        </>
      ) : null}
    </View>
  );
};

export default AccountDetail;
