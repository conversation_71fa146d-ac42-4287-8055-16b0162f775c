import React, { useState, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { Button } from "@taroify/core";
import { User, Plus, Edit } from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { accountApi } from "@/services/api";
import { GameAccount } from "@/types";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import EmptyState from "@/components/EmptyState";
import { showError, formatPower } from "@/utils";
import "./list.scss";

const AccountListPage: React.FC = () => {
  const { isLoggedIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<GameAccount[]>([]);

  useEffect(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadAccounts();
    }
  }, [isLoggedIn]);

  // 页面显示时刷新数据
  useDidShow(() => {
    // 只有登录用户才加载数据
    if (isLoggedIn) {
      loadAccounts();
    }
  });

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountApi.getMyAccounts();
      setAccounts(response.data);
    } catch (error) {
      console.error("加载账号列表失败:", error);
      showError("加载账号列表失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    // 创建账号需要登录
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }
    Taro.navigateTo({
      url: "/pages/game/sanguo/account/form",
    });
  };

  const handleEditAccount = (account: GameAccount) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/account/form?id=${account.id}`,
    });
  };

  const handleAccountDetail = (account: GameAccount) => {
    Taro.navigateTo({
      url: `/pages/game/sanguo/account/detail?id=${account.id}`,
    });
  };

  const handleRefresh = () => {
    loadAccounts();
  };

  return (
    <View className="account-list-page">
      {/* 页面头部 */}
      <View className="page-header">
        <View className="header-info">
          <Text className="page-title">我的账号</Text>
          <Text className="page-subtitle">管理您的游戏账号信息</Text>
        </View>
        <Button
          className="create-btn"
          color="primary"
          size="small"
          icon={<Plus size="32rpx" />}
          onClick={handleCreateAccount}
        >
          创建账号
        </Button>
      </View>

      {/* 账号列表 */}
      <View className="account-list">
        {loading ? (
          <LoadingState text="加载中..." />
        ) : !isLoggedIn ? (
          <EmptyState
            title="请先登录"
            description="登录后查看您的账号信息"
            actionText="立即登录"
            onAction={() => Taro.navigateTo({ url: "/pages/login/index" })}
          />
        ) : accounts.length > 0 ? (
          accounts.map((account) => (
            <PageCard
              key={account.id}
              title={account.accountName}
              description={`区服${account.serverId} · 战力${formatPower(
                account.powerValue
              )}`}
              icon={<User size="40rpx" color="var(--primary-color)" />}
              extra={
                <View className="account-extra">
                  <View className="account-stats">
                    <Text className="stat-item">
                      伤害+{account.damageBonus}%
                    </Text>
                    <Text className="stat-item">兵{account.troopLevel}级</Text>
                  </View>
                  <Button
                    className="edit-btn"
                    size="mini"
                    variant="outlined"
                    icon={<Edit size="24rpx" />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditAccount(account);
                    }}
                  >
                    编辑
                  </Button>
                </View>
              }
              onClick={() => handleAccountDetail(account)}
            >
              <View className="account-details">
                <View className="detail-row">
                  <Text className="detail-label">集结容量:</Text>
                  <Text className="detail-value">
                    {formatPower(account.rallyCapacity)}
                  </Text>
                </View>
                <View className="detail-row">
                  <Text className="detail-label">联盟状态:</Text>
                  <Text className="detail-value alliance-status">
                    {account.allianceId ? "已加入联盟" : "未加入联盟"}
                  </Text>
                </View>
              </View>
            </PageCard>
          ))
        ) : (
          <EmptyState
            title="暂无账号"
            description="创建您的第一个游戏账号开始管理"
            actionText="创建账号"
            onAction={handleCreateAccount}
          />
        )}
      </View>

      {/* 底部操作 */}
      {accounts.length > 0 && (
        <View className="bottom-actions">
          <Button
            className="refresh-btn"
            variant="outlined"
            size="large"
            onClick={handleRefresh}
          >
            刷新列表
          </Button>
        </View>
      )}

      {/* 使用说明 */}
      <View className="usage-tips">
        <Text className="tips-title">使用说明</Text>
        <View className="tips-content">
          <Text className="tip-item">• 每个区服最多可创建2个账号</Text>
          <Text className="tip-item">• 账号信息用于联盟管理和战事安排</Text>
          <Text className="tip-item">• 请确保账号信息的准确性</Text>
        </View>
      </View>
    </View>
  );
};

export default AccountListPage;
