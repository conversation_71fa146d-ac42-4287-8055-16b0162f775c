import React, { useState, useEffect } from "react";
import { View, Input } from "@tarojs/components";
import Taro, { useRouter } from "@tarojs/taro";
import { <PERSON>ton, Cell, Popup, Picker, Stepper, Field } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { accountApi } from "@/services/api";
import { AccountForm, GameAccount } from "@/types";
import {
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  validateForm,
  showConfirm,
} from "@/utils";
import LoadingState from "@/components/LoadingState";
import "./form.scss";
import { ArrowRight } from "@taroify/icons";

const AccountFormPage: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const { serverId } = router.params;

  const [isEdit, setIsEdit] = useState(false);
  const [accountId, setAccountId] = useState<string | null>(null);
  const [accountData, setAccountData] = useState<GameAccount | null>(null);
  const [redirectPath, setRedirectPath] = useState("");

  const [formData, setFormData] = useState<
    AccountForm & { memberTier?: string; lvbuStarLevel?: number }
  >({
    serverId: serverId ? parseInt(serverId) : 0,
    accountName: "",
    powerValue: "0",
    damageBonus: 0,
    troopLevel: 1,
    rallyCapacity: 0,
    memberTier: "TIER_1",
    lvbuStarLevel: 0,
  });

  // 吕布星级选择相关状态
  const [showLvbuPicker, setShowLvbuPicker] = useState(false);

  // 吕布星级选项（0-5星，支持0.5递增）
  const lvbuStarOptions = [
    { label: "0星", value: 0 },
    { label: "0.1星", value: 0.1 },
    { label: "0.2星", value: 0.2 },
    { label: "0.3星", value: 0.3 },
    { label: "0.4星", value: 0.4 },
    { label: "1星", value: 1 },
    { label: "1.1星", value: 1.1 },
    { label: "1.2星", value: 1.2 },
    { label: "1.3星", value: 1.3 },
    { label: "1.4星", value: 1.4 },
    { label: "2星", value: 2 },
    { label: "2.1星", value: 2.1 },
    { label: "2.2星", value: 2.2 },
    { label: "2.3星", value: 2.3 },
    { label: "2.4星", value: 2.4 },
    { label: "3星", value: 3 },
    { label: "3.1星", value: 3.1 },
    { label: "3.2星", value: 3.2 },
    { label: "3.3星", value: 3.3 },
    { label: "3.4星", value: 3.4 },
    { label: "4星", value: 4 },
    { label: "4.1星", value: 4.1 },
    { label: "4.2星", value: 4.2 },
    { label: "4.3星", value: 4.3 },
    { label: "4.4星", value: 4.4 },
    { label: "5星", value: 5 },
  ];

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      return;
    }

    const { id, redirect } = router.params;
    if (id) {
      setIsEdit(true);
      setAccountId(id);
      loadAccountData(id);
    }

    // 保存重定向路径
    if (redirect) {
      setRedirectPath(decodeURIComponent(redirect));
    }
  }, [isLoggedIn, authLoading]);

  const loadAccountData = async (id: string) => {
    try {
      setLoading(true);
      const response = await accountApi.getById(id);
      const account = response.data;
      setAccountData(account);
      setFormData({
        serverId: account.serverId,
        accountName: account.accountName,
        powerValue: account.powerValue,
        damageBonus: account.damageBonus,
        troopLevel: account.troopLevel,
        rallyCapacity: account.rallyCapacity || 0,
        memberTier: account.memberTier || "TIER_1",
        lvbuStarLevel: account.lvbuStarLevel || 0,
      });
    } catch (error) {
      console.error("加载账号数据失败:", error);
      showError("加载账号数据失败");
      Taro.navigateBack();
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field: keyof AccountForm, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateFormData = () => {
    const rules = {
      accountName: {
        required: true,
        message: "请输入账号名称",
      },
      powerValue: {
        required: true,
        min: 0,
        message: "请输入正确的战力值",
      },
      damageBonus: {
        required: true,
        min: 0,
        max: 100,
        message: "加成应在0-100%之间",
      },
      troopLevel: {
        required: true,
        min: 1,
        max: 30,
        message: "兵种等级应在1-30级之间",
      },
      rallyCapacity: {
        min: 0,
        message: "请输入正确的集结容量",
      },
    };

    return validateForm(formData, rules);
  };

  const handleSubmit = async () => {
    const validation = validateFormData();
    if (!validation.valid) {
      showError(validation.errors[0]);
      return;
    }

    try {
      showLoading(isEdit ? "更新中..." : "创建中...");

      if (isEdit && accountId) {
        // 如果账号有联盟且包含成员阶位信息，使用带阶位的更新接口
        if (accountData?.allianceId && formData.memberTier) {
          await accountApi.updateWithTier(accountId, formData);
        } else {
          await accountApi.update(accountId, formData);
        }
        showSuccess("账号更新成功");
      } else {
        await accountApi.create(formData);
        showSuccess("账号创建成功");
      }

      // 根据重定向路径跳转
      if (redirectPath) {
        Taro.redirectTo({
          url: redirectPath,
        });
      } else {
        Taro.navigateBack();
      }
    } catch (error) {
      console.error("提交失败:", error);
      showError(isEdit ? "更新失败" : "创建失败");
    } finally {
      hideLoading();
    }
  };

  const handleDelete = async () => {
    if (!isEdit || !accountId) return;

    const confirmed = await showConfirm(
      "确定要删除这个账号吗？删除后无法恢复。"
    );
    if (!confirmed) return;

    try {
      showLoading("删除中...");

      await accountApi.delete(accountId);
      showSuccess("账号删除成功");
      Taro.navigateBack();
    } catch (error) {
      console.error("删除失败:", error);
      showError("删除失败");
    } finally {
      hideLoading();
    }
  };

  return (
    <View className="account-form-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : (
        <>
          <Cell.Group>
            {/* 区号 */}
            <Cell title="区号">
              <Input
                value={
                  formData.serverId === 0 ? "" : formData.serverId.toString()
                }
                placeholder="请输入区号"
                type="number"
                disabled={isEdit}
                onInput={(e) => {
                  const value = e.detail.value;
                  const numValue = value === "" ? 0 : parseInt(value) || 0;
                  handleFieldChange("serverId", numValue);
                }}
                style={{ textAlign: "right", flex: 1 }}
              />
            </Cell>

            {/* 账号名称 */}
            <Cell title="账号名称">
              <Input
                value={formData.accountName}
                placeholder="请输入账号名称"
                onInput={(e) =>
                  handleFieldChange("accountName", e.detail.value)
                }
                style={{ textAlign: "right", flex: 1 }}
              />
            </Cell>

            {/* 战力值 */}
            <Cell title="战力值">
              <Input
                value={
                  formData.powerValue === "0"
                    ? ""
                    : formData.powerValue.toString()
                }
                placeholder="请输入战力值"
                type="number"
                onInput={(e) => {
                  const value = e.detail.value;
                  const numValue = value === "" ? 0 : parseInt(value) || 0;
                  handleFieldChange("powerValue", numValue);
                }}
                style={{ textAlign: "right", flex: 1 }}
              />
            </Cell>

            {/* 加成 */}
            <Cell title="加成(%)">
              <Input
                value={
                  formData.damageBonus === 0
                    ? ""
                    : formData.damageBonus.toString()
                }
                placeholder="请输入加成(0-100)"
                type="number"
                onInput={(e) => {
                  const value = e.detail.value;
                  const numValue = value === "" ? 0 : value || 0;
                  handleFieldChange("damageBonus", numValue);
                }}
                style={{ textAlign: "right", flex: 1 }}
              />
            </Cell>

            {/* 兵种等级 - 使用 Stepper */}
            <Cell title="兵种等级">
              <Stepper
                value={formData.troopLevel}
                step={1}
                min={1}
                max={50}
                onChange={(value) => handleFieldChange("troopLevel", value)}
              />
            </Cell>

            {/* 吕布星级 */}
            <Cell
              title="吕布星级"
              rightIcon={<ArrowRight />}
              clickable
              onClick={() => setShowLvbuPicker(true)}
            >
              {formData.lvbuStarLevel}星
            </Cell>

            {/* 集结容量 */}
            <Cell title="集结容量">
              <Input
                value={
                  formData.rallyCapacity === 0
                    ? ""
                    : formData.rallyCapacity.toString()
                }
                placeholder="请输入集结容量（选填）"
                type="number"
                onInput={(e) => {
                  const value = e.detail.value;
                  const numValue = value === "" ? 0 : parseInt(value) || 0;
                  handleFieldChange("rallyCapacity", numValue);
                }}
                style={{ textAlign: "right", flex: 1 }}
              />
            </Cell>

            {/* 成员阶位 - 只有加入联盟的账号才显示 */}
            {isEdit && accountData?.allianceId && (
              <Cell title="成员阶位">
                <View className="tier-selector">
                  {["TIER_1", "TIER_2", "TIER_3", "TIER_4", "TIER_5"].map(
                    (tier) => (
                      <Button
                        key={tier}
                        size="mini"
                        color={
                          formData.memberTier === tier ? "primary" : "default"
                        }
                        variant={
                          formData.memberTier === tier
                            ? "contained"
                            : "outlined"
                        }
                        onClick={() => handleFieldChange("memberTier", tier)}
                        style={{ marginRight: "8rpx", marginBottom: "8rpx" }}
                      >
                        {tier.replace("TIER_", "")} 阶
                      </Button>
                    )
                  )}
                </View>
              </Cell>
            )}
          </Cell.Group>

          <View className="form-actions">
            <Button
              className="submit-btn"
              color="primary"
              size="large"
              onClick={handleSubmit}
            >
              {isEdit ? "更新账号" : "创建账号"}
            </Button>

            {isEdit && (
              <Button
                className="delete-btn"
                color="danger"
                size="large"
                variant="outlined"
                onClick={handleDelete}
              >
                删除账号
              </Button>
            )}
          </View>
        </>
      )}

      {/* 吕布星级选择 Popup */}
      <Popup
        open={showLvbuPicker}
        onClose={() => setShowLvbuPicker(false)}
        placement="bottom"
        style={{ height: "40%" }}
      >
        <Picker
          value={[formData.lvbuStarLevel?.toString() || "0"]}
          onConfirm={(values) => {
            const selectedValue = parseFloat(values[0]);
            handleFieldChange("lvbuStarLevel", selectedValue);
            setShowLvbuPicker(false);
          }}
          onCancel={() => setShowLvbuPicker(false)}
        >
          <Picker.Toolbar>
            <Picker.Button>取消</Picker.Button>
            <Picker.Title>选择吕布星级</Picker.Title>
            <Picker.Button>确认</Picker.Button>
          </Picker.Toolbar>
          <Picker.Column>
            {lvbuStarOptions.map((option) => (
              <Picker.Option key={option.value} value={option.value.toString()}>
                {option.label}
              </Picker.Option>
            ))}
          </Picker.Column>
        </Picker>
      </Popup>
    </View>
  );
};

export default AccountFormPage;
