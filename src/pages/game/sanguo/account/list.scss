.account-list-page {
  min-height: 100vh;
  background: var(--background-color);
  padding: 32rpx;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    padding: 32rpx;
    background: var(--surface-color);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx var(--shadow-color);

    .header-info {
      flex: 1;

      .page-title {
        font-size: 40rpx;
        font-weight: 600;
        color: var(--text-primary);
        display: block;
        margin-bottom: 8rpx;
      }

      .page-subtitle {
        font-size: 28rpx;
        color: var(--text-secondary);
        display: block;
      }
    }

    .create-btn {
      border-radius: 24rpx;
      font-size: 28rpx;
    }
  }

  .account-list {
    margin-bottom: 32rpx;

    .account-extra {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 16rpx;

      .account-stats {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8rpx;

        .stat-item {
          font-size: 24rpx;
          color: var(--text-secondary);
          background: rgba(25, 118, 210, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }

      .edit-btn {
        border-radius: 16rpx;
        font-size: 24rpx;
      }
    }

    .account-details {
      margin-top: 16rpx;
      padding-top: 16rpx;
      border-top: 1rpx solid var(--border-color);

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-size: 28rpx;
          color: var(--text-secondary);
        }

        .detail-value {
          font-size: 28rpx;
          color: var(--text-primary);
          font-weight: 500;

          &.alliance-status {
            color: var(--success-color);
          }
        }
      }
    }
  }

  .bottom-actions {
    margin-bottom: 32rpx;

    .refresh-btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
    }
  }

  .usage-tips {
    background: var(--surface-color);
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 12rpx var(--shadow-color);

    .tips-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 24rpx;
      display: block;
    }

    .tips-content {
      .tip-item {
        font-size: 28rpx;
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 16rpx;
        display: block;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
