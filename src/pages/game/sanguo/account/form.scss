.account-form-page {
  min-height: 100vh;
  background: var(--background-color);
  padding: 32rpx;

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120rpx;
    color: var(--text-secondary);
  }

  .form-group {
    margin-bottom: 48rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx var(--shadow-color);

    .form-cell {
      padding: 32rpx 24rpx;
      background: var(--surface-color);
      border-bottom: 1rpx solid var(--border-color);

      &:last-child {
        border-bottom: none;
      }

      .form-label {
        font-size: 32rpx;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 16rpx;
        display: block;
      }

      .form-value {
        font-size: 32rpx;
        color: var(--text-primary);
      }
    }
  }

  .form-actions {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-top: 32rpx;

    .submit-btn {
      height: 96rpx;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 600;
    }

    .delete-btn {
      height: 96rpx;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 600;
    }
  }

  .tier-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8rpx;
    padding: 8rpx 0;
  }
}
