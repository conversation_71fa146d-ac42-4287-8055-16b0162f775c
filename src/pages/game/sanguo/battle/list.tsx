import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { getEnv, useRouter } from "@tarojs/taro";
import {
  Button,
  Tabs,
  Dialog,
  ActionSheet,
  Input,
  Field,
  Form,
  Textarea,
  Cell,
  Checkbox,
  Flex,
} from "@taroify/core";
import {
  Plus,
  Edit,
  Delete,
  User,
  Checked,
  Close,
  Exchange,
  ListSwitch,
} from "@taroify/icons";
import { useAuth } from "@/hooks/useAuth";
import { battleApi, warGroupApi, allianceMemberApi } from "@/services/api";
import { BattleApplication, WarGroup, GameAccount, WarTactic } from "@/types";
import { showError, showSuccess, formatPower, formatDateTime } from "@/utils";
import { PageCard, LoadingState, EmptyState } from "@/components";
import LvbuStarLevel from "@/components/LvbuStarLevel";
import "./list.scss";

const env = getEnv();
const BattleList: React.FC = () => {
  const router = useRouter();
  const { isLoggedIn, loading: authLoading } = useAuth();
  const [allianceId, setAllianceId] = useState<string>("");
  const [activeTab, setActiveTab] = useState(0);
  const [activeSubTab, setActiveSubTab] = useState(0); // 0: 战术安排, 1: 申请列表
  const [switchingTab, setSwitchingTab] = useState(false); // 添加切换状态
  const [pageLoading, setPageLoading] = useState(true); // 页面加载状态

  // 战事安排数据
  const [warArrangements, setWarArrangements] = useState<any[]>([]);
  const [warGroups, setWarGroups] = useState<WarGroup[]>([]);
  const [mobileMembersMap, setMobileMembersMap] = useState<
    Record<string, GameAccount[]>
  >({});

  // 申请数据
  const [guanduOneApplications, setGuanduOneApplications] = useState<
    BattleApplication[]
  >([]);
  const [guanduTwoApplications, setGuanduTwoApplications] = useState<
    BattleApplication[]
  >([]);

  // 对话框状态
  const [showGroupDialog, setShowGroupDialog] = useState(false);
  const [showMemberActionSheet, setShowMemberActionSheet] = useState(false);
  const [showUseTacticSheet, setShowUseTacticSheet] = useState(false);
  const [selectedMember, setSelectedMember] = useState<GameAccount | null>(
    null
  );
  const [editingGroup, setEditingGroup] = useState<WarGroup | null>(null);
  const [groupName, setGroupName] = useState("");
  const [groupTask, setGroupTask] = useState("");

  const [showClearDialog, setShowClearDialog] = useState(false);
  const [sendNotificationAfterClear, setSendNotificationAfterClear] =
    useState(false);

  // 添加人员相关状态
  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);
  const [availableMembers, setAvailableMembers] = useState<GameAccount[]>([]);
  const [loadingMembers, setLoadingMembers] = useState(false);

  // 排序相关状态
  const [sortBy, setSortBy] = useState<"power" | "damage" | "lvbu" | "none">(
    "none"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [showSortActionSheet, setShowSortActionSheet] = useState(false);

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return;

    if (!isLoggedIn) {
      // 未登录，跳转到登录页
      Taro.redirectTo({ url: "/pages/login/index" });
      setPageLoading(false);
      return;
    }

    const { allianceId: paramAllianceId } = router.params;
    if (paramAllianceId) {
      setAllianceId(paramAllianceId);
      // 初始加载默认为官渡一
      loadWarData(paramAllianceId, "GUANDU_ONE").finally(() => {
        setPageLoading(false);
      });
    } else {
      console.error("缺少联盟ID参数");
      showError("缺少联盟ID参数");
      setPageLoading(false);
    }
  }, [isLoggedIn, authLoading]);

  useEffect(() => {
    if (allianceId) {
      loadCurrentTabData();
    }
  }, [allianceId]); // 移除 activeTab 依赖，避免切换时重复加载

  const loadWarData = async (
    aId: string,
    warType?: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE"
  ) => {
    try {
      // 如果没有指定战事类型，默认加载官渡一
      const targetWarType = warType || "GUANDU_ONE";

      // 加载指定战事类型的数据
      const detailRes = await warGroupApi.getArrangementDetail(
        aId,
        targetWarType
      );
      console.log(`${targetWarType}安排详情:`, detailRes.data);

      const arrangements: any[] = [];
      const groups: WarGroup[] = [];

      // 处理分组数据
      if (detailRes.data.warGroups) {
        detailRes.data.warGroups.forEach((groupDetail) => {
          groups.push({
            id: groupDetail.groupId,
            groupName: groupDetail.groupName,
            groupTask: groupDetail.groupTask,
            warType: targetWarType,
            allianceId: aId,
            createdAt: "",
            updatedAt: "",
          });

          // 处理分组成员
          groupDetail.members.forEach((member) => {
            arrangements.push({
              id: `${member.id}-${groupDetail.groupId}`,
              accountId: member.id,
              allianceId: aId,
              warType: targetWarType,
              warGroupId: groupDetail.groupId,
              account: member,
              warGroup: {
                id: groupDetail.groupId,
                groupName: groupDetail.groupName,
                groupTask: groupDetail.groupTask,
              },
            });
          });
        });
      }

      // 处理机动人员
      if (detailRes.data.mobileMembers) {
        detailRes.data.mobileMembers.forEach((member) => {
          arrangements.push({
            id: `${member.id}-mobile-${targetWarType}`,
            accountId: member.id,
            allianceId: aId,
            warType: targetWarType,
            warGroupId: null,
            account: member,
            warGroup: null,
          });
        });
      }

      setWarArrangements(arrangements);
      setWarGroups(groups);

      // 处理机动人员数据
      processMobileMembers(arrangements);

      // 如果是官渡一或官渡二，加载申请数据
      if (targetWarType === "GUANDU_ONE" || targetWarType === "GUANDU_TWO") {
        loadWarApplications(aId, targetWarType);
      } else {
        setGuanduOneApplications([]);
        setGuanduTwoApplications([]);
      }
    } catch (error) {
      console.error("加载战事数据失败:", error);
      showError("加载战事数据失败");
    }
  };

  const loadCurrentTabData = async () => {
    if (!allianceId) return;

    const warType = getWarTypeByTab(activeTab);

    // 加载当前战事类型的数据
    await loadWarData(allianceId, warType);
  };

  const loadWarApplications = async (
    aId: string,
    warType: "GUANDU_ONE" | "GUANDU_TWO"
  ) => {
    try {
      const applicationsRes = await battleApi.getAllianceApplications(
        aId,
        warType
      );
      console.log(`${warType}申请:`, applicationsRes.data);

      if (warType === "GUANDU_ONE") {
        setGuanduOneApplications(applicationsRes.data);
      } else {
        setGuanduTwoApplications(applicationsRes.data);
      }
    } catch (error) {
      console.error("加载申请数据失败:", error);
    }
  };

  // 排序函数
  const sortMembers = (
    members: GameAccount[],
    sortType: "power" | "damage" | "lvbu" | "none",
    order: "asc" | "desc"
  ) => {
    if (sortType === "none") return members;

    return [...members].sort((a, b) => {
      let valueA: number, valueB: number;

      switch (sortType) {
        case "power":
          valueA = parseFloat(a.powerValue) || 0;
          valueB = parseFloat(b.powerValue) || 0;
          break;
        case "damage":
          valueA = a.damageBonus || 0;
          valueB = b.damageBonus || 0;
          break;
        case "lvbu":
          valueA = a.lvbuStarLevel || 0;
          valueB = b.lvbuStarLevel || 0;
          break;
        default:
          return 0;
      }

      const result = valueA - valueB;
      return order === "desc" ? -result : result;
    });
  };

  // 获取排序后的可用成员列表
  const getSortedAvailableMembers = () => {
    return sortMembers(availableMembers, sortBy, sortOrder);
  };

  // 处理排序选择
  const handleSortSelect = (sortType: "power" | "damage" | "lvbu" | "none") => {
    if (sortBy === sortType) {
      // 如果选择相同的排序类型，切换排序顺序
      setSortOrder(sortOrder === "desc" ? "asc" : "desc");
    } else {
      // 选择新的排序类型，默认降序
      setSortBy(sortType);
      setSortOrder("desc");
    }
    setShowSortActionSheet(false);
  };

  const processMobileMembers = (arrangements: any[]) => {
    const mobileMap: Record<string, GameAccount[]> = {};

    arrangements.forEach((arrangement) => {
      if (!arrangement.warGroup && arrangement.account) {
        const warType = arrangement.warType;
        if (!mobileMap[warType]) {
          mobileMap[warType] = [];
        }
        mobileMap[warType].push(arrangement.account);
      }
    });

    setMobileMembersMap(mobileMap);
  };

  const getWarTypeByTab = (
    tabIndex: number
  ): "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE" => {
    const warTypes = ["GUANDU_ONE", "GUANDU_TWO", "SIEGE", "DEFENSE"];
    // @ts-ignore
    return warTypes[tabIndex];
  };

  const getWarTypeName = (warType: string): string => {
    switch (warType) {
      case "GUANDU_ONE":
        return "官渡一";
      case "GUANDU_TWO":
        return "官渡二";
      case "SIEGE":
        return "攻城";
      case "DEFENSE":
        return "守城";
      default:
        return "未知";
    }
  };

  const getCurrentWarGroups = (): WarGroup[] => {
    const warType = getWarTypeByTab(activeTab);
    return warGroups.filter((group) => group.warType === warType);
  };

  const getCurrentMobileMembers = (): GameAccount[] => {
    const warType = getWarTypeByTab(activeTab);
    return mobileMembersMap[warType] || [];
  };

  const getCurrentApplications = (): BattleApplication[] => {
    const warType = getWarTypeByTab(activeTab);
    if (warType === "GUANDU_ONE") return guanduOneApplications;
    if (warType === "GUANDU_TWO") return guanduTwoApplications;
    return [];
  };

  // 获取当前战事安排总人数
  const getCurrentArrangementCount = (): number => {
    const currentGroups = getCurrentWarGroups();
    const mobileMembers = getCurrentMobileMembers();

    let totalCount = mobileMembers.length;
    currentGroups.forEach((group) => {
      const groupMembers = warArrangements.filter(
        (arr) => arr.warGroup?.id === group.id
      );
      totalCount += groupMembers.length;
    });

    return totalCount;
  };

  // 分组管理
  const handleCreateGroup = () => {
    setEditingGroup(null);
    setGroupName("");
    setGroupTask("");
    setShowGroupDialog(true);
  };

  const handleEditGroup = (group: WarGroup) => {
    setEditingGroup(group);
    setGroupName(group.groupName);
    setGroupTask(group.groupTask || "");
    setShowGroupDialog(true);
  };

  const handleSaveGroup = async () => {
    if (!groupName.trim()) {
      showError("请输入分组名称");
      return;
    }

    try {
      const warType = getWarTypeByTab(activeTab);

      if (editingGroup) {
        // 编辑分组
        await warGroupApi.update(editingGroup.id, {
          groupName: groupName.trim(),
          groupTask: groupTask.trim(),
        });
        showSuccess("分组更新成功");
      } else {
        // 创建分组
        await warGroupApi.create({
          groupName: groupName.trim(),
          groupTask: groupTask.trim(),
          warType,
          allianceId,
        });
        showSuccess("分组创建成功");
      }

      setShowGroupDialog(false);
      // 重新加载当前战事类型的数据
      loadWarData(allianceId, warType);
    } catch (error) {
      console.error("保存分组失败:", error);
      showError("保存分组失败");
    }
  };

  const handleDeleteGroup = async (group: WarGroup) => {
    try {
      await warGroupApi.delete(group.id);
      showSuccess("分组删除成功");
      // 重新加载当前战事类型的数据
      const currentWarType = getWarTypeByTab(activeTab);
      loadWarData(allianceId, currentWarType);
    } catch (error) {
      console.error("删除分组失败:", error);
      showError("删除分组失败");
    }
  };

  // 成员移动
  const handleMemberClick = (member: GameAccount) => {
    setSelectedMember(member);
    setShowMemberActionSheet(true);
  };

  // 检查成员是否为机动人员
  const isMemberMobile = (member: GameAccount): boolean => {
    const currentWarType = getWarTypeByTab(activeTab);
    const mobileMembers = mobileMembersMap[currentWarType] || [];
    return mobileMembers.some((m) => m.id === member.id);
  };

  const handleMoveMemberToGroup = async (groupId: string | null) => {
    if (!selectedMember) return;

    try {
      const warType = getWarTypeByTab(activeTab);
      await warGroupApi.arrangeMember({
        accountId: selectedMember.id,
        warType,
        warGroupId: groupId,
      });

      showSuccess(`移动成员成功`);
      setShowMemberActionSheet(false);
      setSelectedMember(null);

      // 重新加载当前战事类型的数据
      const currentWarType = getWarTypeByTab(activeTab);
      loadWarData(allianceId, currentWarType);
    } catch (error) {
      console.error("移动成员失败:", error);
      showError("移动成员失败");
    }
  };

  // 调动成员到另一个官渡战事
  const handleMoveToAnotherGuandu = async () => {
    if (!selectedMember) return;

    try {
      await battleApi.moveWarApplication(selectedMember.id);
      showSuccess("调动成功");
      setShowMemberActionSheet(false);
      setSelectedMember(null);

      // 重新加载当前战事类型的数据
      const currentWarType = getWarTypeByTab(activeTab);
      loadWarData(allianceId, currentWarType);
    } catch (error) {
      console.error("调动失败:", error);
      showError("调动失败");
    }
  };

  // 申请列表
  const handleProcessApplication = async (
    applicationId: string,
    approved: boolean
  ) => {
    try {
      await battleApi.processApplication(applicationId, approved);
      loadCurrentTabData();
    } catch (error) {
      console.error("处理申请失败:", error);
    }
  };

  // 清空战事安排
  const handleClearArrangements = () => {
    // 重置通知选择状态
    setSendNotificationAfterClear(false);
    setShowClearDialog(true);
  };

  const handleConfirmClearArrangements = async () => {
    try {
      const warType = getWarTypeByTab(activeTab);

      // 使用支持可选通知的清空接口
      await warGroupApi.clearArrangementsWithNotification({
        allianceId,
        warType,
        sendNotification: sendNotificationAfterClear,
      });

      showSuccess(
        sendNotificationAfterClear
          ? "战事安排已清空，通知已发送"
          : "战事安排已清空"
      );
      setShowClearDialog(false);

      // 重新加载当前战事类型的数据
      loadWarData(allianceId, warType);
    } catch (error) {
      console.error("清空战事安排失败:", error);
      showError("清空战事安排失败");
    }
  };

  // 添加人员相关方法
  const handleAddMember = async () => {
    try {
      setLoadingMembers(true);
      await loadAvailableMembers();
      setShowAddMemberDialog(true);
    } catch (error) {
      console.error("加载可用成员失败:", error);
      showError("加载可用成员失败");
    } finally {
      setLoadingMembers(false);
    }
  };

  const loadAvailableMembers = async () => {
    try {
      // 获取联盟所有成员
      const response = await allianceMemberApi.getMembers(allianceId);
      const allMembers = response.data;

      // 获取当前战事类型
      const currentWarType = getWarTypeByTab(activeTab);

      // 获取所有战事类型的完整数据
      const allWarData = await Promise.all([
        warGroupApi.getArrangementDetail(allianceId, "GUANDU_ONE"),
        warGroupApi.getArrangementDetail(allianceId, "GUANDU_TWO"),
        warGroupApi.getArrangementDetail(allianceId, "SIEGE"),
        warGroupApi.getArrangementDetail(allianceId, "DEFENSE"),
      ]);

      // 构建所有战事的成员映射
      const allWarMembers: Record<string, Set<string>> = {
        GUANDU_ONE: new Set(),
        GUANDU_TWO: new Set(),
        SIEGE: new Set(),
        DEFENSE: new Set(),
      };

      const warTypes = [
        "GUANDU_ONE",
        "GUANDU_TWO",
        "SIEGE",
        "DEFENSE",
      ] as const;

      allWarData.forEach((warData, index) => {
        const warType = warTypes[index];

        // 添加机动人员
        if (warData.data.mobileMembers) {
          warData.data.mobileMembers.forEach((member) => {
            allWarMembers[warType].add(member.id);
          });
        }

        // 添加分组成员
        if (warData.data.warGroups) {
          warData.data.warGroups.forEach((group) => {
            if (group.members) {
              group.members.forEach((member) => {
                allWarMembers[warType].add(member.id);
              });
            }
          });
        }
      });

      // 获取申请数据
      const [guanduOneApps, guanduTwoApps] = await Promise.all([
        battleApi.getAllianceApplications(allianceId, "GUANDU_ONE"),
        battleApi.getAllianceApplications(allianceId, "GUANDU_TWO"),
      ]);

      const guanduOneApplicationIds = new Set(
        guanduOneApps.data.map((app) => app.account?.id).filter(Boolean)
      );
      const guanduTwoApplicationIds = new Set(
        guanduTwoApps.data.map((app) => app.account?.id).filter(Boolean)
      );

      console.log("所有战事成员数据:", allWarMembers);
      console.log("申请数据:", {
        guanduOneApplicationIds,
        guanduTwoApplicationIds,
      });

      // 过滤已在战事中的人员
      const filteredMembers = allMembers.filter((member) => {
        // 检查是否已在当前战事中
        if (allWarMembers[currentWarType].has(member.id)) {
          console.log(
            `成员 ${member.accountName} 已在当前战事 ${currentWarType} 中`
          );
          return false;
        }

        // 检查是否在当前战事的申请中
        if (
          currentWarType === "GUANDU_ONE" &&
          guanduOneApplicationIds.has(member.id)
        ) {
          console.log(`成员 ${member.accountName} 正在申请官渡一`);
          return false;
        }
        if (
          currentWarType === "GUANDU_TWO" &&
          guanduTwoApplicationIds.has(member.id)
        ) {
          console.log(`成员 ${member.accountName} 正在申请官渡二`);
          return false;
        }

        // 官渡一和官渡二之间的互斥检查
        if (currentWarType === "GUANDU_ONE") {
          // 检查是否已在官渡二中或申请中
          if (allWarMembers["GUANDU_TWO"].has(member.id)) {
            console.log(
              `成员 ${member.accountName} 已在官渡二中，不能加入官渡一`
            );
            return false;
          }
          if (guanduTwoApplicationIds.has(member.id)) {
            console.log(
              `成员 ${member.accountName} 已申请官渡二，不能加入官渡一`
            );
            return false;
          }
        } else if (currentWarType === "GUANDU_TWO") {
          // 检查是否已在官渡一中或申请中
          if (allWarMembers["GUANDU_ONE"].has(member.id)) {
            console.log(
              `成员 ${member.accountName} 已在官渡一中，不能加入官渡二`
            );
            return false;
          }
          if (guanduOneApplicationIds.has(member.id)) {
            console.log(
              `成员 ${member.accountName} 已申请官渡一，不能加入官渡二`
            );
            return false;
          }
        }

        return true;
      });

      console.log(`过滤后可用成员数量: ${filteredMembers.length}`);

      // 按加成倒序排列
      const sortedMembers = filteredMembers.sort(
        (a, b) => (b.damageBonus || 0) - (a.damageBonus || 0)
      );

      setAvailableMembers(sortedMembers);
    } catch (error) {
      console.error("加载可用成员失败:", error);
      throw error;
    }
  };

  const handleSelectMember = async (member: GameAccount) => {
    try {
      const warType = getWarTypeByTab(activeTab);
      await battleApi.addToWar(member.id, warType);
      showSuccess(`已添加 ${member.accountName} 到${getWarTypeName(warType)}`);
      setShowAddMemberDialog(false);

      // 重新加载当前战事类型的数据
      loadWarData(allianceId, warType);
    } catch (error) {
      console.error("添加成员失败:", error);
      showError("添加成员失败");
    }
  };

  // 移出当前战事
  const handleRemoveFromWar = async () => {
    if (!selectedMember) return;

    try {
      const warType = getWarTypeByTab(activeTab);
      await battleApi.removeFromWar(selectedMember.id, warType);
      showSuccess(
        `已将 ${selectedMember.accountName} 移出${getWarTypeName(warType)}`
      );
      setShowMemberActionSheet(false);
      setSelectedMember(null);

      // 重新加载当前战事类型的数据
      loadWarData(allianceId, warType);
    } catch (error) {
      console.error("移出战事失败:", error);
      showError("移出战事失败");
    }
  };

  const getMemberTierName = (tier: string) => {
    return tier ? `${tier.replace("TIER_", "")}阶` : "1阶";
  };

  // 渲染战术安排
  const renderTacticalArrangement = (isGuandu?: boolean) => {
    const currentGroups = getCurrentWarGroups();
    const mobileMembers = getCurrentMobileMembers();

    return (
      <View className="tactical-arrangement">
        {/* 切换时的加载状态 */}
        {switchingTab ? (
          <View className="switching-loading">
            <LoadingState text="切换中..." />
          </View>
        ) : (
          <>
            {/* 分组管理按钮 */}
            <View className="arrangement-header">
              <View className="header-left">
                <Button
                  color="primary"
                  size="small"
                  icon={<Plus />}
                  onClick={handleCreateGroup}
                >
                  添加分组
                </Button>
                <Button
                  color="success"
                  size="small"
                  icon={<User />}
                  onClick={handleAddMember}
                  loading={loadingMembers}
                >
                  添加人员
                </Button>
                {isGuandu && (
                  <Button
                    color="primary"
                    size="small"
                    icon={<ListSwitch />}
                    onClick={() => setShowUseTacticSheet(true)}
                  >
                    预制战术
                  </Button>
                )}
              </View>
              <View className="header-right">
                <Button
                  color="danger"
                  size="small"
                  variant="outlined"
                  icon={<Delete />}
                  onClick={handleClearArrangements}
                >
                  清空人员
                </Button>
              </View>
            </View>

            {/* 分组列表 */}
            {currentGroups.map((group) => {
              const groupMembers = warArrangements.filter(
                (arr) => arr.warGroup?.id === group.id
              );

              return (
                <View key={group.id} className="war-group">
                  <View className="group-header">
                    <View className="group-title-row">
                      <Text className="group-name">{group.groupName}</Text>
                      <View className="group-actions">
                        <Button
                          size="mini"
                          variant="outlined"
                          icon={<Edit />}
                          onClick={() => handleEditGroup(group)}
                        >
                          编辑
                        </Button>
                        <Button
                          size="mini"
                          color="danger"
                          variant="outlined"
                          icon={<Delete />}
                          onClick={() => handleDeleteGroup(group)}
                        >
                          删除
                        </Button>
                      </View>
                    </View>
                    {group.groupTask && (
                      <View className="group-task">{group.groupTask}</View>
                    )}
                  </View>
                  <View className="group-members">
                    {groupMembers.length > 0 ? (
                      <View className="members-grid">
                        {groupMembers.map((arrangement) => (
                          <View
                            key={arrangement.id}
                            className="member-card"
                            onClick={() =>
                              handleMemberClick(arrangement.account)
                            }
                          >
                            <View className="member-name">
                              {getMemberTierName(
                                arrangement.account?.memberTier
                              )}
                              _{arrangement.account?.accountName || "未知"}
                            </View>
                            <View className="member-stats">
                              <Flex>
                                <Flex.Item span={7} className="member-power">
                                  {formatPower(arrangement.account?.powerValue)}
                                </Flex.Item>
                                <Flex.Item span={6} className="member-bonus">
                                  +{arrangement.account?.damageBonus || 0}%
                                </Flex.Item>
                                <Flex.Item span={6} className="member-troop">
                                  {arrangement.account?.troopLevel || 1}级
                                </Flex.Item>
                                <Flex.Item span={5} className="member-lvbu">
                                  <LvbuStarLevel
                                    level={
                                      arrangement.account?.lvbuStarLevel || 0
                                    }
                                    size="small"
                                  />
                                </Flex.Item>
                              </Flex>
                              {arrangement.account?.applicationTime && (
                                <View className="member-application-time">
                                  <Text className="application-time-text">
                                    申请时间: {formatDateTime(arrangement.account.applicationTime)}
                                  </Text>
                                </View>
                              )}
                            </View>
                          </View>
                        ))}
                      </View>
                    ) : (
                      <View className="empty-group">暂无成员</View>
                    )}
                  </View>
                </View>
              );
            })}

            {/* 机动人员 */}
            <View className="mobile-members">
              <View className="mobile-header">
                <Text className="mobile-title">
                  🚀 机动人员 ({mobileMembers.length})
                </Text>
                <Text className="mobile-subtitle">点击成员可移动到分组</Text>
              </View>
              <View className="mobile-member-grid">
                {mobileMembers.map((member) => (
                  <View
                    key={member.id}
                    className="mobile-member-card"
                    onClick={() => handleMemberClick(member)}
                  >
                    <View className="member-name">
                      {getMemberTierName(member.memberTier)}_
                      {member.accountName}
                    </View>
                    <View className="member-stats">
                      <Flex>
                        <Flex.Item span={7} className="member-power">
                          {formatPower(member.powerValue)}
                        </Flex.Item>
                        <Flex.Item span={6} className="member-bonus">
                          +{member.damageBonus || 0}%
                        </Flex.Item>
                        <Flex.Item span={6} className="member-troop">
                          {member.troopLevel || 1}级
                        </Flex.Item>
                        <Flex.Item span={5} className="member-lvbu">
                          <LvbuStarLevel
                            level={member.lvbuStarLevel || 0}
                            size="small"
                          />
                        </Flex.Item>
                      </Flex>
                      {member.applicationTime && (
                        <View className="member-application-time">
                          <Text className="application-time-text">
                            申请时间: {formatDateTime(member.applicationTime)}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </>
        )}
      </View>
    );
  };

  // 渲染申请列表
  const renderApplicationManagement = () => {
    const applications = getCurrentApplications();

    return (
      <View className="application-management">
        {applications.length > 0 ? (
          applications.map((application) => (
            <PageCard
              key={application.id}
              title={application.account?.accountName || "未知账号"}
              description={`战力: ${formatPower(
                application.account?.powerValue
              )} · 加成：${application.account?.damageBonus}% \n 申请时间: ${
                application.createdAt
              }`}
              icon={<User size="40rpx" color="var(--warning-color)" />}
              extra={
                application.status === "PENDING" ? (
                  <View className="application-actions">
                    <Button
                      size="mini"
                      color="success"
                      icon={<Checked />}
                      onClick={() =>
                        handleProcessApplication(application.id, true)
                      }
                    >
                      通过
                    </Button>
                    <Button
                      size="mini"
                      color="danger"
                      variant="outlined"
                      icon={<Close />}
                      onClick={() =>
                        handleProcessApplication(application.id, false)
                      }
                      style={{ marginLeft: "16rpx" }}
                    >
                      拒绝
                    </Button>
                  </View>
                ) : (
                  <Text
                    className={`status-text status-${application.status.toLowerCase()}`}
                  >
                    {application.status === "APPROVED" ? "已通过" : "已拒绝"}
                  </Text>
                )
              }
            />
          ))
        ) : (
          <EmptyState title="暂无申请" description="目前没有申请记录" />
        )}
      </View>
    );
  };

  // 渲染战事类型内容
  const renderWarTypeContent = (warType: string) => {
    const isGuanDu = warType === "GUANDU_ONE" || warType === "GUANDU_TWO";

    return (
      <View className="war-type-content">
        {isGuanDu ? (
          // 有申请列表的战事类型（官渡一、官渡二）
          <Tabs
            value={activeSubTab}
            onChange={(value) => {
              setActiveSubTab(value);
              // 如果切换到申请列表标签页，加载申请数据
              if (value === 1) {
                const moveWarType = getWarTypeByTab(activeTab);
                if (
                  moveWarType === "GUANDU_ONE" ||
                  moveWarType === "GUANDU_TWO"
                ) {
                  loadWarApplications(allianceId, moveWarType);
                }
              }
            }}
          >
            <Tabs.TabPane
              title={`战术安排 (${getCurrentArrangementCount()}人)`}
            >
              {renderTacticalArrangement(isGuanDu)}
            </Tabs.TabPane>
            <Tabs.TabPane
              title={`申请列表 (${getCurrentApplications().length}人)`}
            >
              {renderApplicationManagement()}
            </Tabs.TabPane>
          </Tabs>
        ) : (
          // 只有战术安排的战事类型（攻城、守城）
          renderTacticalArrangement()
        )}
      </View>
    );
  };

  const handleUseTactic = async (tactic: string) => {
    const currentWarType = getWarTypeByTab(activeTab);
    try {
      await battleApi.useTactic(allianceId, {
        warType: currentWarType,
        tactic: tactic as WarTactic,
      });
    } catch (e) {
      showError("预制战术使用失败～");
    } finally {
      setShowUseTacticSheet(false);
      // 重新加载当前战事类型的数据
      loadWarData(allianceId, currentWarType);
      showSuccess("预制战术使用成功！");
    }
  };

  const getTacticName = (tactic: string) => {
    switch (tactic) {
      case "HOLD_GRAIN":
        return "抱粮守仓";
      case "CUT_SUPPLY":
        return "断粮抢米";
      case "MULTI_ATTACK":
        return "多点开花";
      case "STEAL_POINT":
        return "顺手牵羊";
      default:
        return "未知战术";
    }
  };

  return (
    <View className="battle-list-page">
      {pageLoading ? (
        <LoadingState text="加载中..." />
      ) : !allianceId ? (
        <EmptyState
          title="参数错误"
          description="缺少联盟ID参数"
          actionText="返回"
          onAction={() => Taro.navigateBack()}
        />
      ) : (
        <>
          <View className="page-header">
            <View className="header-content">
              <Text className="page-title">联盟战事安排</Text>
            </View>
          </View>

          {/* 战事类型标签页 */}
          <Tabs
            value={activeTab}
            onChange={async (value) => {
              if (value === activeTab) return; // 如果是相同标签页，不执行切换

              setSwitchingTab(true); // 开始切换状态
              setActiveTab(value);
              setActiveSubTab(0); // 切换战事类型时重置子标签页

              try {
                // 根据切换的标签页加载对应的战事数据
                const warType = getWarTypeByTab(value);
                await loadWarData(allianceId, warType);
              } finally {
                setSwitchingTab(false); // 结束切换状态
              }
            }}
          >
            <Tabs.TabPane title={getWarTypeName("GUANDU_ONE")}>
              {renderWarTypeContent("GUANDU_ONE")}
            </Tabs.TabPane>

            <Tabs.TabPane title={getWarTypeName("GUANDU_TWO")}>
              {renderWarTypeContent("GUANDU_TWO")}
            </Tabs.TabPane>

            <Tabs.TabPane title={getWarTypeName("SIEGE")}>
              {renderWarTypeContent("SIEGE")}
            </Tabs.TabPane>

            <Tabs.TabPane title={getWarTypeName("DEFENSE")}>
              {renderWarTypeContent("DEFENSE")}
            </Tabs.TabPane>
          </Tabs>

          {/* 分组编辑对话框 */}
          <Dialog
            open={showGroupDialog}
            onClose={() => setShowGroupDialog(false)}
            title={editingGroup ? "编辑分组" : "添加分组"}
          >
            <Dialog.Content>
              <Form>
                <Cell.Group
                  inset
                  style={{
                    // @ts-ignore
                    "--textarea-line-height": env === "WEB" ? "1.2rem" : "1",
                  }}
                >
                  <Field label="分组名称">
                    <Input
                      value={groupName}
                      onChange={(e) => setGroupName(e.detail.value)}
                      placeholder="分组名称"
                    />
                  </Field>
                  <Field label="分组任务">
                    <Textarea
                      autoHeight
                      value={groupTask}
                      onChange={(e) => setGroupTask(e.detail.value)}
                      placeholder="分组任务（可选）"
                    />
                  </Field>
                </Cell.Group>
              </Form>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowGroupDialog(false)}>取消</Button>
              <Button color="primary" onClick={handleSaveGroup}>
                {editingGroup ? "更新" : "创建"}
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 成员移动选择器 */}
          <ActionSheet
            open={showMemberActionSheet}
            onClose={() => setShowMemberActionSheet(false)}
          >
            <ActionSheet.Header>
              移动成员: {selectedMember?.accountName}
            </ActionSheet.Header>

            {/* 只有非机动人员才显示移动到机动人员选项 */}
            {selectedMember && !isMemberMobile(selectedMember) && (
              <ActionSheet.Action
                onClick={() => handleMoveMemberToGroup(null)}
                style={{ color: "#667eea", fontWeight: "bold" }}
              >
                🚀 移动到机动人员
              </ActionSheet.Action>
            )}

            {/* 机动人员的特殊操作 */}
            {selectedMember && isMemberMobile(selectedMember) && (
              <>
                {/* 官渡一和官渡二的机动人员可以调动到另一个官渡战事 */}
                {(getWarTypeByTab(activeTab) === "GUANDU_ONE" ||
                  getWarTypeByTab(activeTab) === "GUANDU_TWO") && (
                  <ActionSheet.Action
                    onClick={handleMoveToAnotherGuandu}
                    style={{ color: "#ff6b35", fontWeight: "bold" }}
                  >
                    <Exchange size="16rpx" style={{ marginRight: "8rpx" }} />
                    调动到
                    {getWarTypeByTab(activeTab) === "GUANDU_ONE"
                      ? "官渡二"
                      : "官渡一"}
                  </ActionSheet.Action>
                )}

                {/* 移出当前战事选项 */}
                <ActionSheet.Action
                  onClick={handleRemoveFromWar}
                  style={{ color: "#ff4757", fontWeight: "bold" }}
                >
                  <Close size="16rpx" style={{ marginRight: "8rpx" }} />
                  移出当前战事
                </ActionSheet.Action>
              </>
            )}

            {/* 分组选项（过滤掉当前成员已在的分组） */}
            {getCurrentWarGroups()
              .filter((group) => {
                // 查找当前成员是否在这个分组中
                const memberInGroup = warArrangements.find(
                  (arr) =>
                    arr.account?.id === selectedMember?.id &&
                    arr.warGroup?.id === group.id
                );
                return !memberInGroup; // 只显示成员不在的分组
              })
              .map((group) => (
                <ActionSheet.Action
                  key={group.id}
                  onClick={() => handleMoveMemberToGroup(group.id)}
                >
                  📁 {group.groupName}
                </ActionSheet.Action>
              ))}

            <ActionSheet.Action
              onClick={() => setShowMemberActionSheet(false)}
              style={{ color: "#999" }}
            >
              取消
            </ActionSheet.Action>
          </ActionSheet>

          {/* 使用预制战术对话框 */}
          <ActionSheet
            open={showUseTacticSheet}
            onClose={() => setShowUseTacticSheet(false)}
          >
            <ActionSheet.Header>选个预制战术吧～！</ActionSheet.Header>
            {Object.values(WarTactic).map((tactic) => (
              <ActionSheet.Action
                key={tactic}
                onClick={() => handleUseTactic(tactic)}
              >
                {getTacticName(tactic)}
              </ActionSheet.Action>
            ))}
            <ActionSheet.Action
              onClick={() => setShowUseTacticSheet(false)}
              style={{ color: "#999" }}
            >
              取消
            </ActionSheet.Action>
          </ActionSheet>

          {/* 清空战事安排确认对话框 */}
          <Dialog
            open={showClearDialog}
            onClose={() => setShowClearDialog(false)}
            title="确认清空战事安排"
          >
            <Dialog.Content>
              <View style={{ padding: "10rpx 0" }}>
                <Text style={{ display: "block", marginBottom: "24rpx" }}>
                  确定要清空当前{getWarTypeName(getWarTypeByTab(activeTab))}
                  的所有战事安排吗？
                </Text>

                {/* 只有官渡战事才显示通知选项 */}
                {(getWarTypeByTab(activeTab) === "GUANDU_ONE" ||
                  getWarTypeByTab(activeTab) === "GUANDU_TWO") && (
                  <View style={{ marginTop: "24rpx" }}>
                    <Checkbox
                      checked={sendNotificationAfterClear}
                      onChange={(checked) =>
                        setSendNotificationAfterClear(checked)
                      }
                    >
                      <Text style={{ fontSize: "28rpx", color: "#666" }}>
                        清空后给联盟成员发送官渡报名通知
                      </Text>
                      <View>
                        <Text style={{ fontSize: "24rpx", color: "#999" }}>
                          将同时清空
                          {getWarTypeByTab(activeTab) === "GUANDU_ONE"
                            ? "官渡二"
                            : "官渡一"}
                          的战事安排
                        </Text>
                      </View>
                    </Checkbox>
                  </View>
                )}
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowClearDialog(false)}>取消</Button>
              <Button color="danger" onClick={handleConfirmClearArrangements}>
                确认清空
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 添加人员对话框 */}
          <Dialog
            open={showAddMemberDialog}
            onClose={() => setShowAddMemberDialog(false)}
            title={`添加人员到${getWarTypeName(getWarTypeByTab(activeTab))}`}
          >
            <Dialog.Content>
              {/* 排序控制 */}
              <View className="sort-controls">
                <Button
                  size="mini"
                  color="primary"
                  variant="outlined"
                  onClick={() => setShowSortActionSheet(true)}
                >
                  排序:{" "}
                  {sortBy === "none"
                    ? "默认"
                    : sortBy === "power"
                    ? "战力"
                    : sortBy === "damage"
                    ? "加成"
                    : "吕布星级"}
                  {sortBy !== "none" && (sortOrder === "desc" ? " ↓" : " ↑")}
                </Button>
              </View>

              <View className="member-list">
                {availableMembers.length > 0 ? (
                  getSortedAvailableMembers().map((member) => (
                    <Cell
                      key={member.id}
                      style={{ padding: "5rpx" }}
                      title={member.accountName}
                      titleStyle={{ flex: 12, textAlign: "left" }}
                      brief={
                        <View className="member-brief">
                          <Text>
                            战: {formatPower(member.powerValue)} · 兵
                            {member.troopLevel}级 · +{member.damageBonus || 0}%
                            · 吕布:
                            <LvbuStarLevel
                              level={member.lvbuStarLevel || 0}
                              size="small"
                            />
                          </Text>
                        </View>
                      }
                      icon={<User size="40rpx" color="var(--primary-color)" />}
                      clickable
                      onClick={() => handleSelectMember(member)}
                    />
                  ))
                ) : (
                  <View className="empty-state">
                    <Text>暂无可添加的成员</Text>
                  </View>
                )}
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onClick={() => setShowAddMemberDialog(false)}>
                取消
              </Button>
            </Dialog.Actions>
          </Dialog>

          {/* 排序选择 ActionSheet */}
          <ActionSheet
            open={showSortActionSheet}
            onClose={() => setShowSortActionSheet(false)}
            onCancel={() => setShowSortActionSheet(false)}
          >
            <ActionSheet.Header>选择排序方式</ActionSheet.Header>
            <ActionSheet.Action onClick={() => handleSortSelect("none")}>
              默认排序
            </ActionSheet.Action>
            <ActionSheet.Action onClick={() => handleSortSelect("power")}>
              按战力排序{" "}
              {sortBy === "power" && (sortOrder === "desc" ? "↓" : "↑")}
            </ActionSheet.Action>
            <ActionSheet.Action onClick={() => handleSortSelect("damage")}>
              按加成排序{" "}
              {sortBy === "damage" && (sortOrder === "desc" ? "↓" : "↑")}
            </ActionSheet.Action>
            <ActionSheet.Action onClick={() => handleSortSelect("lvbu")}>
              按吕布星级排序{" "}
              {sortBy === "lvbu" && (sortOrder === "desc" ? "↓" : "↑")}
            </ActionSheet.Action>
          </ActionSheet>
        </>
      )}
    </View>
  );
};

export default BattleList;
