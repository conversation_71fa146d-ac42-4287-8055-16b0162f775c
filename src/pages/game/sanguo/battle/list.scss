.battle-list-page {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .page-header {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(25, 118, 210, 0.1);

    .header-content {
      text-align: center;

      .page-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #212121;
        margin-bottom: 8rpx;
      }
    }
  }

  .war-type-content {
    padding: 20rpx 0;
  }

  .tactical-arrangement {
    .switching-loading {
      padding: 80rpx 0;
      text-align: center;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12rpx;
      margin: 20rpx 0;
    }

    .arrangement-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      margin-top: 20rpx;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }
    }

    .sort-controls {
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      margin-bottom: 16rpx;
    }

    .member-list {
      max-height: 400rpx;
      overflow-y: auto;

      .member-brief {
        display: flex;
        flex-direction: column;
        gap: 8rpx;
      }

      .empty-state {
        text-align: center;
        padding: 60rpx 0;
        color: var(--text-secondary);
      }
    }

    .war-group {
      background: white;
      border-radius: 12rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border: 2rpx solid #e8f4fd;
      transition: all 0.3s ease;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: #1976d2;
        z-index: 1;
      }

      .group-header {
        background: white;
        padding: 20rpx 24rpx;
        border-bottom: 1rpx solid #f5f5f5;

        .group-title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12rpx;

          .group-name {
            font-size: 30rpx;
            font-weight: 700;
            color: #333;
            flex: 1;
          }

          .group-actions {
            display: flex;
            gap: 8rpx;
            align-items: center;

            .taroify-button {
              font-size: 22rpx;
              padding: 6rpx 12rpx;
              border-radius: 6rpx;

              &[color="danger"] {
                border-color: #ff4757;
                color: #ff4757;

                &:hover {
                  background: #ff4757;
                  color: white;
                }
              }

              &:not([color="danger"]) {
                border-color: #667eea;
                color: #667eea;

                &:hover {
                  background: #667eea;
                  color: white;
                }
              }
            }
          }
        }

        .group-task {
          font-size: 24rpx;
          color: #666;
          line-height: 1.5;
          background: #f8f9fa;
          padding: 12rpx 16rpx;
          border-radius: 8rpx;
          border-left: 3rpx solid #667eea;
          margin-top: 6rpx;
          word-wrap: break-word;
          white-space: pre-wrap;
        }
      }

      .group-members {
        padding: 16rpx;
        background: #fafafa;

        .members-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12rpx;
        }

        .member-card {
          background: white;
          border-radius: 12rpx;
          padding: 16rpx;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
          border: 1rpx solid #e8e8e8;
          transition: all 0.2s ease;
          cursor: pointer;
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3rpx;
            background: #64b5f6;
          }

          &:hover {
            transform: translateY(-1rpx);
            box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
            border-color: #667eea;
          }

          &:active {
            transform: translateY(0);
          }

          .member-name {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 6rpx;
            display: flex;
            align-items: center;
            gap: 6rpx;

            &::before {
              content: "👤";
              font-size: 22rpx;
            }
          }

          .member-stats {
            font-size: 22rpx;
            color: #666;

            .member-power {
              font-size: 20rpx;
              color: #1976d2;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .member-bonus {
              font-size: 20rpx;
              color: #ff5722;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .member-troop {
              font-size: 20rpx;
              color: #4caf50;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .member-lvbu {
              font-size: 20rpx;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .member-application-time {
            margin-top: 8rpx;
            padding-top: 8rpx;
            border-top: 1rpx solid #f0f0f0;

            .application-time-text {
              font-size: 20rpx;
              color: #999;
              text-align: center;
            }
          }
        }

        .empty-group {
          text-align: center;
          padding: 40rpx 20rpx;
          color: #999;
          font-size: 28rpx;
          background: white;
          border-radius: 16rpx;
          border: 2rpx dashed #ddd;

          &::before {
            content: "👥";
            display: block;
            font-size: 48rpx;
            margin-bottom: 16rpx;
            opacity: 0.5;
          }
        }
      }
    }

    .mobile-members {
      margin-top: 20rpx;
      background: white;
      border-radius: 12rpx;
      padding: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
      border: 2rpx solid #e8f4fd;

      .mobile-header {
        text-align: center;
        margin-bottom: 16rpx;
        padding-bottom: 12rpx;
        border-bottom: 1rpx dashed #ddd;

        .mobile-title {
          font-size: 28rpx;
          font-weight: 700;
          color: #667eea;
          margin-bottom: 6rpx;
          display: block;
        }

        .mobile-subtitle {
          font-size: 22rpx;
          color: #999;
          display: block;
        }
      }

      .mobile-member-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12rpx;
      }

      .mobile-member-card {
        background: white;
        border-radius: 12rpx;
        padding: 16rpx;
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
        border: 1rpx solid #e8e8e8;
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3rpx;
          background: #64b5f6;
        }

        &:hover {
          transform: translateY(-1rpx);
          box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
          border-color: #667eea;
        }

        &:active {
          transform: translateY(0);
        }

        .member-name {
          font-size: 26rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 6rpx;
          display: flex;
          align-items: center;
          gap: 6rpx;

          &::before {
            content: "👤";
            font-size: 22rpx;
          }
        }

        .member-stats {
          font-size: 22rpx;
          color: #666;

          .member-power {
            font-size: 20rpx;
            color: #1976d2;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .member-bonus {
            font-size: 20rpx;
            color: #ff5722;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .member-troop {
            font-size: 20rpx;
            color: #4caf50;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .member-lvbu {
            font-size: 20rpx;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .member-application-time {
          margin-top: 8rpx;
          padding-top: 8rpx;
          border-top: 1rpx solid #f0f0f0;

          .application-time-text {
            font-size: 20rpx;
            color: #999;
            text-align: center;
          }
        }
      }
    }
  }

  .application-management {
    .application-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }

    .status-text {
      font-size: 24rpx;
      font-weight: 500;

      &.status-approved {
        color: var(--success-color);
      }

      &.status-rejected {
        color: var(--danger-color);
      }
    }
  }
}
