.login-page {
  min-height: 100vh;
  background: #1976d2;
  position: relative;
  overflow: hidden;

  .login-container {
    position: relative;
    z-index: 2;
    padding: 80rpx 48rpx 48rpx;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .login-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 400rpx;
    z-index: 1;

    .login-bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 300rpx;
        height: 300rpx;
        top: -150rpx;
        right: -100rpx;
      }

      &.circle-2 {
        width: 200rpx;
        height: 200rpx;
        top: 100rpx;
        left: -80rpx;
      }

      &.circle-3 {
        width: 150rpx;
        height: 150rpx;
        top: 200rpx;
        right: 100rpx;
      }
    }
  }

  .login-logo-section {
    text-align: center;
    margin-top: 120rpx;
    margin-bottom: 80rpx;

    .login-logo {
      width: 160rpx;
      height: 160rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 40rpx;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    }

    .login-title {
      font-size: 48rpx;
      font-weight: 700;
      color: white;
      display: block;
      margin-bottom: 16rpx;
    }

    .login-subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      display: block;
    }
  }

  .login-features {
    display: flex;
    justify-content: space-around;
    margin-bottom: 80rpx;
    padding: 0 40rpx;

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32rpx 24rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 16rpx;
      backdrop-filter: blur(10rpx);

      .feature-text {
        font-size: 24rpx;
        color: white;
        margin-top: 16rpx;
      }
    }
  }

  .login-actions {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 60rpx;

    .login-btn {
      width: 100%;
      height: 96rpx;
      background: white;
      color: #1976d2;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 600;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
      margin-bottom: 32rpx;

      &:active {
        transform: scale(0.98);
      }
    }

    .login-tips {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
      line-height: 1.5;
    }
  }

  .login-games {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16rpx;
    padding: 32rpx;
    backdrop-filter: blur(10rpx);

    .games-title {
      font-size: 28rpx;
      color: white;
      font-weight: 600;
      display: block;
      margin-bottom: 24rpx;
    }

    .games-list {
      .game-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 0;

        .game-name {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.9);
        }

        .game-status {
          font-size: 24rpx;
          color: #4caf50;
          background: rgba(76, 175, 80, 0.2);
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
        }
      }
    }
  }
}
