import React, { useState } from "react";
import { View, Text } from "@tarojs/components";
import { Button } from "@taroify/core";
import { ShieldOutlined, User } from "@taroify/icons";
import Taro, { useRouter } from "@tarojs/taro";
import { authApi } from "@/services/api";
import { showError, showLoading, hideLoading } from "@/utils";
import "./index.scss";

const Login: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // 微信登录 - 使用 getUserInfo 获取用户信息
  const handleWechatLogin = async () => {
    try {
      setLoading(true);
      showLoading("登录中...");

      // 获取微信登录凭证
      const loginRes = await Taro.login();
      if (!loginRes.code) {
        throw new Error("获取登录凭证失败");
      }

      // 使用 getUserInfo 获取用户信息（不需要用户授权）
      let nickname = "微信用户";
      let avatarUrl = "";

      try {
        const userInfo = await Taro.getUserInfo();
        console.log("用户信息:", userInfo);

        if (userInfo.userInfo) {
          nickname = userInfo.userInfo.nickName || "微信用户";
          avatarUrl = userInfo.userInfo.avatarUrl || "";
        }
      } catch (getUserInfoError) {
        console.log("获取用户信息失败，使用默认信息:", getUserInfoError);
        // 继续使用默认信息进行登录
      }

      // 调用后端登录接口
      const response = await authApi.login({
        code: loginRes.code,
        nickname: nickname,
        avatarUrl: avatarUrl,
      });

      // 保存登录信息
      Taro.setStorageSync("token", response.data.token);
      Taro.setStorageSync("userInfo", {
        userId: response.data.userId,
        nickname: response.data.nickname,
        avatarUrl: response.data.avatarUrl,
        isNewUser: response.data.isNewUser,
      });

      console.log("登录成功，保存的用户信息:", {
        token: response.data.token,
        userInfo: {
          userId: response.data.userId,
          nickname: response.data.nickname,
          avatarUrl: response.data.avatarUrl,
          isNewUser: response.data.isNewUser,
        },
      });

      // 检查是否有重定向参数
      const { redirect } = router.params;
      if (redirect) {
        // 有重定向参数，跳转到指定页面
        const redirectUrl = decodeURIComponent(redirect);
        Taro.redirectTo({
          url: redirectUrl,
        });
      } else {
        // 没有重定向参数，跳转到首页
        Taro.redirectTo({
          url: "/pages/index/index",
        });
      }
    } catch (error) {
      console.error("登录失败:", error);
      showError(error.message || "登录失败，请重试");
    } finally {
      setLoading(false);
      hideLoading();
    }
  };

  return (
    <View className="login-page">
      <View className="login-container">
        {/* 顶部装饰 */}
        <View className="login-header">
          <View className="login-bg-circle circle-1"></View>
          <View className="login-bg-circle circle-2"></View>
          <View className="login-bg-circle circle-3"></View>
        </View>

        {/* Logo 和标题 */}
        <View className="login-logo-section">
          <View className="login-logo">
            <ShieldOutlined size="120rpx" color="#1976d2" />
          </View>
          <Text className="login-title">战策宝</Text>
          <Text className="login-subtitle">专业的游戏联盟管理平台</Text>
        </View>

        {/* 功能介绍 */}
        <View className="login-features">
          <View className="feature-item">
            <User size="40rpx" color="white" />
            <Text className="feature-text">账号管理</Text>
          </View>
          <View className="feature-item">
            <ShieldOutlined size="40rpx" color="white" />
            <Text className="feature-text">联盟管理</Text>
          </View>
          <View className="feature-item">
            <ShieldOutlined size="40rpx" color="white" />
            <Text className="feature-text">战事安排</Text>
          </View>
        </View>

        {/* 登录按钮 */}
        <View className="login-actions">
          <Button
            className="login-btn"
            color="primary"
            size="large"
            loading={loading}
            disabled={loading}
            onClick={handleWechatLogin}
          >
            微信一键登录
          </Button>

          <Text className="login-tips">
            {/* 登录即表示同意《用户协议》和《隐私政策》 */}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default Login;
