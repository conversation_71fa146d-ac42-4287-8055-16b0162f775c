.index-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;
  color: #333;

  .test-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin: 24rpx 0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    text {
      display: block;
      margin: 16rpx 0;
      font-size: 28rpx;
      line-height: 1.5;
    }
  }

  .debug-info {
    background: #e3f2fd;
    border-radius: 16rpx;
    padding: 32rpx;
    margin: 24rpx 0;
    border-left: 8rpx solid #1976d2;

    text {
      display: block;
      margin: 12rpx 0;
      font-size: 26rpx;
      line-height: 1.4;
      color: #1565c0;

      &:first-child {
        font-weight: 600;
        font-size: 30rpx;
        margin-bottom: 20rpx;
      }
    }
  }

  .user-section {
    margin-bottom: 48rpx;
  }

  .games-section {
    margin-bottom: 48rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #212121;
      margin-bottom: 32rpx;
    }

    .game-card {
      background: #ffffff;
      border-radius: 16rpx;
      margin-top: 24rpx;
      padding: 32rpx;
      box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
      display: flex;
      align-items: center;
      transition: all 0.2s ease;
      border: 2rpx solid #e3f2fd;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.1);
      }

      .game-icon {
        width: 60rpx;
        height: 60rpx;
        background: #82bbf5;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        flex-shrink: 0;

        background: #ffffff;
        border: 2rpx solid #1976d2;
      }

      .game-info {
        flex: 1;

        .game-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #212121;
          margin-bottom: 8rpx;
          display: block;
        }

        .game-desc {
          font-size: 26rpx;
          color: #616161;
          margin-bottom: 16rpx;
          display: block;
          line-height: 1.4;
        }

        .game-status {
          .status-badge {
            font-size: 22rpx;
            padding: 6rpx 12rpx;
            border-radius: 8rpx;
            display: inline-block;

            &.active {
              background: #4caf50;
              color: white;
              font-weight: 500;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }

            &.coming_soon {
              background: #ff9800;
              color: white;
              font-weight: 500;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }
          }
        }
      }

      .game-arrow {
        color: #999999;
        font-size: 24rpx;
        flex-shrink: 0;
      }
    }
  }

  .features-section {
    .section-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #212121;
      margin-bottom: 32rpx;
    }

    .features-grid {
      display: grid;
      margin-top: 16rpx;
      grid-template-columns: repeat(3, 1fr);
      gap: 24rpx;

      .feature-item {
        background: #ffffff;
        border-radius: 16rpx;
        padding: 32rpx 24rpx;
        text-align: center;
        box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
        transition: all 0.3s ease;

        &.clickable {
          cursor: pointer;

          &:active {
            transform: scale(0.95);
            box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.15);
          }
        }

        .feature-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #212121;
          margin: 16rpx 0 8rpx;
          display: block;
        }

        .feature-desc {
          font-size: 24rpx;
          color: #616161;
          line-height: 1.4;
          display: block;
        }
      }
    }
  }
}
