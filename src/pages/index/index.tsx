import React, { useState, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import { ArrowRight, User, Bell, Plus, Delete } from "@taroify/icons";
import { <PERSON><PERSON>, Dialog } from "@taroify/core";
import { useAuth } from "@/hooks/useAuth";
import { messageSubscriptionApi } from "@/services/api";
import PageCard from "@/components/PageCard";
import LoadingState from "@/components/LoadingState";
import { showError, showSuccess } from "@/utils";
import "./index.scss";

interface Game {
  id: string;
  name: string;
  description?: string;
  status: "active" | "coming_soon";
  icon: string;
  route: string;
}

const Index: React.FC = () => {
  console.log("Index页面渲染开始");

  // 添加错误边界
  let authResult: any;
  try {
    authResult = useAuth();
    console.log("useAuth hook 调用成功:", authResult);
  } catch (error) {
    console.error("useAuth hook 调用失败:", error);
    // 如果useAuth失败，使用默认值
    authResult = {
      isLoggedIn: false,
      user: null,
      loading: false,
    };
  }

  const { isLoggedIn, user, loading } = authResult || {};

  // 消息订阅相关状态
  const [subscriptionCount, setSubscriptionCount] = useState<number>(0);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);

  const [games] = useState<Game[]>([
    {
      id: "sanguo",
      name: "三国·冰河时代",
      status: "active",
      icon: "🎮",
      route: "/pages/game/sanguo/index",
    },
  ]);

  // 添加调试信息
  console.log("Index页面状态:", { isLoggedIn, user, loading });

  // 页面进入时更新数据
  useEffect(() => {
    if (isLoggedIn && !loading) {
      console.log("用户已登录，开始加载数据");
      loadSubscriptionCount();
    }
  }, [isLoggedIn, loading]);

  // 页面显示时刷新数据
  useDidShow(() => {
    if (isLoggedIn && !loading) {
      console.log("页面显示，刷新数据");
      loadSubscriptionCount();
    }
  });

  const loadSubscriptionCount = async () => {
    try {
      const response = await messageSubscriptionApi.getSubscriptionCount();
      setSubscriptionCount(response.data);
    } catch (error) {
      console.error("加载消息订阅数量失败:", error);
    }
  };

  // 增加消息订阅数量
  const handleAddSubscription = async () => {
    try {
      setSubscriptionLoading(true);

      Taro.requestSubscribeMessage({
        tmplIds: [
          // 这里需要配置实际的模板ID，暂时使用示例ID
          "i4cXxSfmC7eSUaZxRL4u8sT_eBPCQ5RV_xmW0tmV85c",
        ],
        entityIds: [],
        success: async function (res) {
          console.log("订阅消息授权结果:", res);
          if (res["i4cXxSfmC7eSUaZxRL4u8sT_eBPCQ5RV_xmW0tmV85c"] === "accept") {
            // 用户同意授权后，调用接口增加次数
            await messageSubscriptionApi.addSubscriptionCount();
            loadSubscriptionCount(); // 重新加载数量
          }
        },
      });
    } catch (error) {
      console.error("增加订阅数量失败:", error);
      if (
        error.errMsg &&
        error.errMsg.includes("requestSubscribeMessage:fail")
      ) {
        showError("订阅消息授权失败，请重试");
      } else {
        showError("增加订阅数量失败");
      }
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // 清空消息订阅数量
  const handleClearSubscription = async () => {
    try {
      setSubscriptionLoading(true);
      await messageSubscriptionApi.clearSubscription();
      showSuccess("订阅数量已清空");
      setSubscriptionCount(0);
      setShowClearDialog(false);
    } catch (error) {
      console.error("清空订阅数量失败:", error);
      showError("清空订阅数量失败");
    } finally {
      setSubscriptionLoading(false);
    }
  };

  const handleGameClick = (game: Game) => {
    // 未登录用户也可以进入游戏页面
    if (game.status === "active") {
      Taro.navigateTo({
        url: game.route,
      });
    } else {
      Taro.showToast({
        title: "敬请期待",
        icon: "none",
      });
    }
  };

  const handleUserProfile = () => {
    Taro.showToast({
      title: "个人中心开发中",
      icon: "none",
    });
  };

  console.log("渲染主页面内容");
  return (
    <View className="index-page">
      {loading ? (
        <LoadingState text="加载中..." />
      ) : (
        <>
          {/* 用户信息卡片 */}
          <View className="user-section">
            <PageCard
              title={isLoggedIn ? user?.nickname || "游戏玩家" : "游客"}
              description={
                isLoggedIn ? "欢迎使用战策宝" : "点击登录获得完整体验"
              }
              icon={<User size="48rpx" color="#1976d2" />}
              onClick={
                isLoggedIn
                  ? handleUserProfile
                  : () => Taro.navigateTo({ url: "/pages/login/index" })
              }
            />
          </View>

          {/* 消息订阅管理 */}
          {isLoggedIn && (
            <View className="subscription-section">
              <PageCard
                title="订阅小程序通知"
                description={`剩余 ${subscriptionCount} 条订阅通知`}
                icon={<Bell size="48rpx" color="#ff9800" />}
                extra={
                  <View className="subscription-actions">
                    <Button
                      size="mini"
                      color="primary"
                      variant="outlined"
                      icon={<Plus size="20rpx" />}
                      loading={subscriptionLoading}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddSubscription();
                      }}
                      style={{ marginRight: "16rpx" }}
                    >
                      订阅
                    </Button>
                    <Button
                      size="mini"
                      color="danger"
                      variant="outlined"
                      icon={<Delete size="20rpx" />}
                      disabled={subscriptionCount === 0 || subscriptionLoading}
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowClearDialog(true);
                      }}
                    >
                      清空
                    </Button>
                  </View>
                }
              />
            </View>
          )}

          {/* 游戏列表 */}
          <View className="games-section">
            <Text className="section-title">游戏列表</Text>

            {games.map((game) => (
              <View
                key={game.id}
                className="game-card"
                onClick={() => handleGameClick(game)}
              >
                <View className="game-icon">
                  <Text style={{ fontSize: "32rpx" }}>{game.icon}</Text>
                </View>
                <View className="game-info">
                  <Text className="game-title">{game.name}</Text>
                  {game.description && <Text className="game-desc">{game.description}</Text>}
                  <View className="game-status">
                    <Text className={`status-badge ${game.status}`}>
                      {game.status === "active" ? "已支持" : "敬请期待"}
                    </Text>
                  </View>
                </View>
                <View className="game-arrow">
                  <ArrowRight />
                </View>
              </View>
            ))}
          </View>

          {/* 功能介绍 */}
          <View className="features-section">
            <View className="features-grid">
              <View className="feature-item">
                <User size="40rpx" color="#1976d2" />
                <Text className="feature-title">账号管理</Text>
                <Text className="feature-desc">管理游戏账号</Text>
              </View>
              <View className="feature-item">
                <Text style={{ fontSize: "40rpx" }}>🛡️</Text>
                <Text className="feature-title">联盟管理</Text>
                <Text className="feature-desc">创建和管理联盟</Text>
              </View>
              <View className="feature-item">
                <Text style={{ fontSize: "40rpx" }}>⚔️</Text>
                <Text className="feature-title">战事安排</Text>
                <Text className="feature-desc">安排战事人员</Text>
              </View>
            </View>
          </View>
        </>
      )}

      {/* 清空订阅确认对话框 */}
      <Dialog
        open={showClearDialog}
        onClose={() => setShowClearDialog(false)}
        title="确认清空"
      >
        <Dialog.Content>
          <Text>确定要清空所有消息订阅数量吗？清空后将无法接收通知。</Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onClick={() => setShowClearDialog(false)}>取消</Button>
          <Button
            color="danger"
            loading={subscriptionLoading}
            onClick={handleClearSubscription}
          >
            确认清空
          </Button>
        </Dialog.Actions>
      </Dialog>
    </View>
  );
};

export default Index;
