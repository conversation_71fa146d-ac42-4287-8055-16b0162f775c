// 用户相关类型
export interface User {
  id: number;
  openid: string;
  nickname: string;
  avatarUrl: string;
  createdAt: string;
  updatedAt: string;
}

// 游戏账号类型
export interface GameAccount {
  id: string;
  userId: string;
  serverId: number;
  accountName: string;
  powerValue: string;
  damageBonus: number;
  troopLevel: number;
  rallyCapacity?: number; // 集结容量改为可选
  memberTier: string;
  lvbuStarLevel?: number; // 吕布星级，0-5星，支持0.5递增，默认为0
  allianceId?: string;
  alliance?: Alliance;
  createdAt: string;
  updatedAt: string;
}

// 联盟类型
export interface Alliance {
  id: string;
  serverId: number;
  name: string;
  code: string;
  leaderId: string;
  memberCount: number;
  createdAt: string;
  updatedAt: string;
  leader?: User;
  allianceJoinApprovalRequired?: boolean;
  warJoinApprovalRequired?: boolean;
  guanduOneLimit?: number;
  guanduTwoLimit?: number;
}

// 联盟成员类型
export interface AllianceMember {
  id: number;
  allianceId: number;
  accountId: number;
  memberTier: number; // 1-5阶
  joinedAt: string;
  account?: GameAccount;
  user?: User;
}

// 联盟申请类型
export interface AllianceApplication {
  id: string;
  allianceId: string;
  accountId: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  processedBy?: string;
  account?: GameAccount;
  alliance?: Alliance;
  processor?: User;
}

// 战事类型
export enum BattleType {
  GUANDU_ONE = "GUANDU_ONE",
  GUANDU_TWO = "GUANDU_TWO",
  SIEGE = "SIEGE",
  DEFENSE = "DEFENSE",
}

// 战术类型
export enum WarTactic {
  HOLD_GRAIN = "HOLD_GRAIN",
  CUT_SUPPLY = "CUT_SUPPLY",
  MULTI_ATTACK = "MULTI_ATTACK",
  STEAL_POINT = "STEAL_POINT",
}

// 战事分组类型
export interface WarGroup {
  id: string;
  createdAt: string;
  updatedAt: string;
  allianceId: string;
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  groupName: string;
  groupTask: string;
  alliance?: Alliance;
}

// 战事分组详情类型
export interface WarGroupDetail {
  groupId: string;
  groupName: string;
  groupTask: string;
  members: GameAccount[];
}

// 战事人员安排类型
export interface WarArrangement {
  id: string;
  createdAt: string;
  updatedAt: string;
  accountId: string;
  allianceId: string;
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  warGroupId?: string;
  account?: GameAccount;
  alliance?: Alliance;
  warGroup?: WarGroup;
}

// 战事人员安排响应类型
export interface WarArrangementResponse {
  allianceId: string;
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  mobileMembers: GameAccount[];
  warGroups: WarGroupDetail[];
}

// 账号战事安排响应类型
export interface AccountWarArrangementResponse {
  accountId: string;
  account: GameAccount;
  warArrangements: AccountWarArrangementDetail[];
}

// 账号战事安排详情类型
export interface AccountWarArrangementDetail {
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  isMobile: boolean;
  warGroup?: {
    groupId: string;
    groupName: string;
    groupTask: string;
    members: GameAccount[];
  };
}

// 战事申请类型
export interface BattleApplication {
  id: string;
  allianceId: string;
  accountId: string;
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  processedBy?: string;
  account?: GameAccount;
  alliance?: Alliance;
  processor?: User;
}

// 战事申请类型别名（兼容性）
export type WarApplication = BattleApplication;

// 战事分组类型
export interface BattleGroup {
  id: number;
  allianceId: number;
  battleType: BattleType;
  groupName: string;
  groupTask: string;
  memberCount: number;
  createdAt: string;
  updatedAt: string;
}

// 战事分组成员类型
export interface BattleGroupMember {
  id: number;
  groupId: number;
  accountId: number;
  assignedAt: string;
  assignedBy: number;
  account?: GameAccount;
  assigner?: User;
}

// 战事安排类型
export interface BattleArrangement {
  id: number;
  allianceId: number;
  battleType: BattleType;
  accountId: number;
  groupId?: number;
  assignedAt: string;
  assignedBy: number;
  account?: GameAccount;
  group?: BattleGroup;
  assigner?: User;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PageResponse<T = any> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// 登录请求类型
export interface LoginRequest {
  code: string;
  nickname: string;
  avatarUrl: string;
}

// 登录响应类型
export interface LoginResponse {
  token: string;
  userId: string;
  nickname: string;
  avatarUrl: string;
  isNewUser: boolean;
}

// 游戏账号表单类型
export interface AccountForm {
  serverId: number;
  accountName: string;
  powerValue: string;
  damageBonus: number;
  troopLevel: number;
  rallyCapacity: number;
  memberTier?: string;
  lvbuStarLevel?: number;
}

// 联盟表单类型
export interface AllianceForm {
  serverId: number;
  name: string;
}

// 战事分组表单类型
export interface BattleGroupForm {
  battleType: BattleType;
  groupName: string;
  groupTask: string;
}

// 用户数据汇总类型
export interface UserSummary {
  alliances: Alliance[];
  dynasties: Dynasty[];
  gameAccounts: GameAccount[];
}

// 王朝相关类型
export interface Dynasty {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  code: string;
  serverId: number;
  emperorId: string;
  grabEnabled: boolean;
  emperor: User;
}

export interface DynastyPosition {
  id: string;
  createdAt: string;
  updatedAt: string;
  dynastyId: string;
  positionType: "TAI_WEI" | "SHANG_SHU_LING";
  grabStartTime: string;
  grabEndTime: string;
  dutyDate: string;
  disabledTimeSlots: string;
  dynasty: Dynasty;
}

export interface PositionGrab {
  id: string;
  createdAt: string;
  updatedAt: string;
  dynastyId: string;
  positionType: "TAI_WEI" | "SHANG_SHU_LING";
  dutyDate: string;
  timeSlot: number;
  accountId: string;
  dynasty: Dynasty;
  account: GameAccount;
}

export interface DynastyDetailResponse {
  dynasty: Dynasty;
  positions: DynastyPosition[];
  positionGrabs: Record<string, PositionGrab[]>;
}

export interface PositionGrabResultResponse {
  dynastyId: string;
  dutyDate: string;
  grabResults: Record<string, PositionGrab[]>;
  availableTimeSlots: Record<string, number[]>;
}

// 请求类型
export interface CreateDynastyRequest {
  name: string;
  serverId: number;
}

export interface UpdateDynastyRequest {
  name: string;
}

export interface JoinDynastyRequest {
  dynastyCode: string;
}

export interface GrabPositionRequest {
  positionType: "TAI_WEI" | "SHANG_SHU_LING";
  timeSlot: number;
}

export interface SetPositionGrabTimeRequest {
  positionType: "TAI_WEI" | "SHANG_SHU_LING";
  grabStartTime: string;
  grabEndTime: string;
  dutyDate: string;
  disabledTimeSlots?: number[];
}

// 联盟详情类型
export interface AllianceDetail {
  alliance: Alliance;
  members: AllianceMember[];
  battleArrangements: {
    [key in BattleType]: {
      groups: BattleGroup[];
      ungroupedMembers: BattleArrangement[];
    };
  };
}

// 常用枚举
export enum MemberTier {
  TIER_1 = 1,
  TIER_2 = 2,
  TIER_3 = 3,
  TIER_4 = 4,
  TIER_5 = 5,
}

export enum ApplicationStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

// 战事类型
export interface Battle {
  id: string;
  title: string;
  type: "guandu1" | "guandu2" | "siege" | "defense";
  serverId: number;
  startTime: string;
  endTime?: string;
  status: "recruiting" | "preparing" | "ongoing" | "completed";
  maxParticipants: number;
  participantCount?: number;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 战事表单类型
export interface BattleForm {
  title: string;
  type: "guandu1" | "guandu2" | "siege" | "defense";
  serverId: number;
  startTime: string;
  maxParticipants: number;
  description?: string;
}

// 战事参与者类型
export interface BattleParticipant {
  id: string;
  battleId: string;
  accountId: string;
  accountName: string;
  powerValue: number;
  role: "leader" | "member";
  status: "applied" | "approved" | "rejected";
  joinedAt: string;
}

// 战事申请类型
export interface BattleApplicationForm {
  battleId: string;
  accountId: string;
  accountName: string;
  serverId: number;
  powerValue: number;
  message?: string;
}

// 联盟使用预制战术
export interface UseTacticRequest {
  warType: "GUANDU_ONE" | "GUANDU_TWO" | "SIEGE" | "DEFENSE";
  tactic: WarTactic;
}
