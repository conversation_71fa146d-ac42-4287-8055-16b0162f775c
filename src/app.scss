@import "@taroify/core/index.scss";

// 蓝色调主题 - 直接使用颜色值
// 主色调: #1976d2
// 文字颜色: #212121 (主要), #616161 (次要)
// 背景色: #f8f9fa (页面), #ffffff (卡片)
// 边框色: #e3f2fd

// 全局样式重置
view,
text,
button,
input,
textarea,
picker,
scroll-view,
swiper,
swiper-item,
movable-area,
movable-view,
cover-view,
cover-image {
  box-sizing: border-box;
}

page {
  background-color: #f8f9fa;
  color: #212121;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  line-height: 1.5;
}

// 通用工具类
.container {
  padding: 32rpx;
}

.card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212121;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #1976d2;
}

.text-secondary {
  color: #616161;
}

.text-success {
  color: #43a047;
}

.text-warning {
  color: #fb8c00;
}

.text-error {
  color: #e53935;
}

.mb-16 {
  margin-bottom: 32rpx;
}

.mb-24 {
  margin-bottom: 48rpx;
}

.mt-16 {
  margin-top: 32rpx;
}

.mt-24 {
  margin-top: 48rpx;
}

.p-16 {
  padding: 32rpx;
}

.p-24 {
  padding: 48rpx;
}

// 游戏卡片样式 - 纯色蓝色调
.game-card {
  background: #1976d2;
  color: white;
  padding: 48rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  border: 2rpx solid #64b5f6;
  box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.1);

  .game-title {
    font-size: 40rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    color: white;
  }

  .game-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
  }
}
