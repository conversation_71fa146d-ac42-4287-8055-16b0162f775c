import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import { User } from "@/types";

interface AuthState {
  isLoggedIn: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isLoggedIn: false,
    user: null,
    token: null,
    loading: true,
  });

  // 检查 token 是否过期（假设 JWT，exp 字段为秒）
  const isTokenExpired = (token: string): boolean => {
    try {
      const payload = JSON.parse(
        decodeURIComponent(
          atob(token.split(".")[1].replace(/-/g, "+").replace(/_/g, "/"))
        )
      );
      if (payload.exp) {
        return Date.now() / 1000 > payload.exp;
      }
      return false;
    } catch {
      return false;
    }
  };

  // 检查登录状态（优先本地缓存）
  const checkAuthStatus = async () => {
    try {
      const token = Taro.getStorageSync("token");
      const userInfo = Taro.getStorageSync("userInfo");

      if (token && userInfo && !isTokenExpired(token)) {
        setAuthState({
          isLoggedIn: true,
          user: userInfo,
          token,
          loading: false,
        });
        return;
      }

      // token 不存在或已过期，清除本地缓存，但不自动跳转登录页
      Taro.removeStorageSync("token");
      Taro.removeStorageSync("userInfo");
      setAuthState({
        isLoggedIn: false,
        user: null,
        token: null,
        loading: false,
      });
    } catch (error) {
      console.error("检查登录状态失败:", error);
      setAuthState({
        isLoggedIn: false,
        user: null,
        token: null,
        loading: false,
      });
    }
  };

  // 登录
  const login = (token: string, user: User) => {
    Taro.setStorageSync("token", token);
    Taro.setStorageSync("userInfo", user);
    setAuthState({
      isLoggedIn: true,
      user,
      token,
      loading: false,
    });
  };

  // 登出
  const logout = async () => {
    try {
      Taro.removeStorageSync("token");
      Taro.removeStorageSync("userInfo");
      setAuthState({
        isLoggedIn: false,
        user: null,
        token: null,
        loading: false,
      });

      // 只有在当前页面不是登录页时才跳转
      const currentPages = Taro.getCurrentPages();
      const currentPage = currentPages[currentPages.length - 1];
      if (currentPage && !currentPage.route?.includes("login")) {
        Taro.redirectTo({
          url: "/pages/login/index",
        });
      }
    } catch (error) {
      console.error("登出失败:", error);
    }
  };

  // 更新用户信息
  const updateUser = (user: User) => {
    Taro.setStorageSync("userInfo", user);
    setAuthState((prev) => ({
      ...prev,
      user,
    }));
  };

  // 检查是否需要登录（不自动跳转）
  const requireAuth = () => {
    return authState.isLoggedIn && !authState.loading;
  };

  // 要求登录并跳转到登录页
  const requireAuthWithRedirect = () => {
    if (!authState.isLoggedIn && !authState.loading) {
      Taro.redirectTo({
        url: "/pages/login/index",
      });
      return false;
    }
    return true;
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  return {
    ...authState,
    login,
    logout,
    updateUser,
    requireAuth,
    requireAuthWithRedirect,
    checkAuthStatus,
  };
};
