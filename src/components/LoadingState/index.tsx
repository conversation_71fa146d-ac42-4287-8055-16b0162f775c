import React from 'react'
import { View, Text } from '@tarojs/components'
import { Loading } from '@taroify/core'
import './index.scss'

interface LoadingStateProps {
  text?: string
  size?: string
  color?: string
}

const LoadingState: React.FC<LoadingStateProps> = ({
  text = '加载中...',
  size = '48rpx',
}) => {
  return (
    <View className="loading-state">
      <Loading size={size} />
      <Text className="loading-text">{text}</Text>
    </View>
  )
}

export default LoadingState
