.page-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.1);
  margin-bottom: 24rpx;
  overflow: hidden;

  &.clickable {
    transition: all 0.2s ease;
    
    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }

  .page-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;

    .page-card-title-area {
      display: flex;
      align-items: center;
      flex: 1;

      .page-card-icon {
        margin-right: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .page-card-title-content {
        flex: 1;

        .page-card-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #212121;
          display: block;
          margin-bottom: 8rpx;
        }

        .page-card-description {
          font-size: 26rpx;
          color: #616161;
          display: block;
          line-height: 1.4;
        }
      }
    }

    .page-card-extra {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }
  }

  .page-card-content {
    padding: 0 32rpx 32rpx;
  }
}
