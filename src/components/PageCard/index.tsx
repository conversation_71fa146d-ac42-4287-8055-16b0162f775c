import React from 'react'
import { View, Text } from '@tarojs/components'
import { ArrowRight } from '@taroify/icons'
import './index.scss'

interface PageCardProps {
  title: string
  description?: string
  icon?: React.ReactNode
  extra?: React.ReactNode
  onClick?: () => void
  children?: React.ReactNode
  className?: string
}

const PageCard: React.FC<PageCardProps> = ({
  title,
  description,
  icon,
  extra,
  onClick,
  children,
  className = ''
}) => {
  return (
    <View 
      className={`page-card ${className} ${onClick ? 'clickable' : ''}`}
      onClick={onClick}
    >
      <View className="page-card-header">
        <View className="page-card-title-area">
          {icon && <View className="page-card-icon">{icon}</View>}
          <View className="page-card-title-content">
            <Text className="page-card-title">{title}</Text>
            {description && (
              <Text className="page-card-description">{description}</Text>
            )}
          </View>
        </View>
        <View className="page-card-extra">
          {extra}
          {onClick && <ArrowRight size="32rpx" color="#bdbdbd" />}
        </View>
      </View>
      {children && (
        <View className="page-card-content">
          {children}
        </View>
      )}
    </View>
  )
}

export default PageCard
