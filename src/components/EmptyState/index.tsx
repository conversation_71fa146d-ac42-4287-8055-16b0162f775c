import React from 'react'
import { View, Text } from '@tarojs/components'
import { Button } from '@taroify/core'
import { InfoOutlined } from '@taroify/icons'
import './index.scss'

interface EmptyStateProps {
  icon?: React.ReactNode
  title?: string
  description?: string
  actionText?: string
  onAction?: () => void
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title = '暂无数据',
  description,
  actionText,
  onAction
}) => {
  return (
    <View className="empty-state">
      <View className="empty-icon">
        {icon || <InfoOutlined size="120rpx" />}
      </View>
      <Text className="empty-title">{title}</Text>
      {description && (
        <Text className="empty-description">{description}</Text>
      )}
      {actionText && onAction && (
        <Button
          className="empty-action"
          color="primary"
          size="small"
          onClick={onAction}
        >
          {actionText}
        </Button>
      )}
    </View>
  )
}

export default EmptyState
