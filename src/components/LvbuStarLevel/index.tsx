import React from "react";
import { Text } from "@tarojs/components";
import "./index.scss";

interface LvbuStarLevelProps {
  level?: number; // 吕布星级，0-5星，支持0.5递增
  size?: "small" | "medium" | "large";
}

const LvbuStarLevel: React.FC<LvbuStarLevelProps> = ({
  level = 0,
  size = "medium",
}) => {
  return <Text className={`lvbu-star-level ${size}`}>{level} 星</Text>;
};

export default LvbuStarLevel;
