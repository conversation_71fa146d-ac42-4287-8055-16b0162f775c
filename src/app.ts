import { PropsWithChildren } from "react";
import { useLaunch, getUpdateManager, showModal } from "@tarojs/taro";

import "./app.scss";

function App({ children }: PropsWithChildren<any>) {
  useLaunch(() => {
    console.log("App launched.");

    // 仅在小程序环境执行
    if (process.env.TARO_ENV === "weapp") {
      const updateManager = getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        console.log("是否有新版本:", res.hasUpdate);
      });

      updateManager.onUpdateReady(() => {
        showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          },
        });
      });

      updateManager.onUpdateFailed(() => {
        showModal({
          title: "更新失败",
          content: "新版本下载失败，请删除小程序后重新打开。",
          showCancel: false,
        });
      });
    }
  });

  return children;
}

export default App;
