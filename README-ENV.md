# 环境配置说明

## 问题描述

在使用 `set NODE_ENV=production && taro build --type weapp` 命令时，API 请求地址仍然是 `http://localhost:8080/api`，没有切换到生产环境地址。

## 解决方案

现在已经安装了 `cross-env`，可以正常使用 `process.env.NODE_ENV` 来配置环境。

### 🎯 推荐方案：使用构建脚本

使用以下命令来构建不同环境：

**生产环境构建：**

```bash
npm run build:weapp:prod
```

**测试环境构建：**

```bash
npm run build:weapp:test
```

**开发环境构建：**

```bash
npm run build:weapp
```

### 🔧 备用方案：直接使用命令行

如果不想使用预定义的脚本，也可以直接使用：

**Windows:**

```bash
set NODE_ENV=production && taro build --type weapp
```

**macOS/Linux:**

```bash
NODE_ENV=production taro build --type weapp
```

## 环境对应的 API 地址

- **开发环境 (development)**: `http://localhost:8080/api`
- **生产环境 (production)**: `https://game-hub-180609-4-1374650229.sh.run.tcloudbase.com/api`
- **测试环境 (test)**: `https://test-api.yourdomain.com/api`

## 调试信息

构建时会在控制台输出环境检测信息，包括：

- NODE_ENV 值
- APP_ENV 值
- 最终选择的环境
- 最终使用的 API 地址

查看这些信息可以帮助确认环境配置是否正确。

## 注意事项

1. 如果使用方案 1，请确保已安装 `cross-env` 依赖
2. 如果使用方案 2，记得在发布前将环境改回 `development`
3. 可以通过查看构建后的代码或运行时日志来确认 API 地址是否正确
