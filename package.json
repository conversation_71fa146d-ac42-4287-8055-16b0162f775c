{"name": "game-hub-weapp", "version": "1.0.0", "private": true, "description": "战策宝", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"prepare": "husky", "new": "taro new", "build:weapp": "taro build --type weapp", "build:weapp:prod": "cross-env NODE_ENV=production APP_ENV=production taro build --type weapp", "build:weapp:test": "cross-env NODE_ENV=test APP_ENV=test taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:h5:prod": "cross-env NODE_ENV=production APP_ENV=production taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@taroify/core": "^0.9.0", "@taroify/icons": "^0.9.0", "@tarojs/components": "4.1.5", "@tarojs/helper": "4.1.5", "@tarojs/plugin-framework-react": "4.1.5", "@tarojs/plugin-platform-alipay": "4.1.5", "@tarojs/plugin-platform-h5": "4.1.5", "@tarojs/plugin-platform-harmony-hybrid": "4.1.5", "@tarojs/plugin-platform-jd": "4.1.5", "@tarojs/plugin-platform-qq": "4.1.5", "@tarojs/plugin-platform-swan": "4.1.5", "@tarojs/plugin-platform-tt": "4.1.5", "@tarojs/plugin-platform-weapp": "4.1.5", "@tarojs/react": "4.1.5", "@tarojs/runtime": "4.1.5", "@tarojs/shared": "4.1.5", "@tarojs/taro": "4.1.5", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@babel/preset-react": "^7.24.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.1.5", "@tarojs/plugin-generator": "4.1.5", "@tarojs/taro-loader": "4.1.5", "@tarojs/webpack5-runner": "4.1.5", "@types/node": "^18", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.1.5", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-taro": "4.1.5", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.4.38", "react-refresh": "^0.14.0", "sass": "^1.75.0", "stylelint": "^16.4.0", "stylelint-config-standard": "^38.0.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "webpack": "5.91.0"}}